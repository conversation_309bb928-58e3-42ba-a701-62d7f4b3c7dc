#!/usr/bin/python
# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from urllib import request
import urllib3
import urllib
from urllib3.exceptions import InsecureRequestWarning

urllib3.disable_warnings(InsecureRequestWarning)
http = urllib3.PoolManager(timeout=urllib3.Timeout(connect=1, read=2))
from uuid import uuid4
import time
import ujson
import traceback  # 输出报错
from pprint import pprint

import requests
from urllib import parse
import json
from datetime import datetime
import hmac
import base64
from hashlib import sha256

from main.config import *
from main.hanshu import *


class zoomex():

    def __init__(self, apiKey=0, colo=0, ccyBi=0, symbols={}):
        self.debug = 0
        self.debugs = []
        self.ccyBi = ccyBi if ccyBi else CcyBi


        keys = {
            'TEST': 'HMF_CI=6bf265e5f0aa8258f6298f9c66699d1760db72e885e0112a8b9f7d2aada5b266ff82e205dcbd8fb2d54ddb7ad28ee9124825bbe24002513bb527054b067b2d0fb8; _gcl_aw=GCL.1688180089.CjwKCAjw-vmkBhBMEiwAlrMeFxWfa40OLWMc6wkCMBOoya5su7JXH5iN7UR6kEWhpMMxoljVgdqomBoC7QkQAvD_BwE; _gcl_au=1.1.90724047.1688180089; _gac_UA-217069923-1=1.1688180089.CjwKCAjw-vmkBhBMEiwAlrMeFxWfa40OLWMc6wkCMBOoya5su7JXH5iN7UR6kEWhpMMxoljVgdqomBoC7QkQAvD_BwE; _gac_UA-175969282-93=1.1688180089.CjwKCAjw-vmkBhBMEiwAlrMeFxWfa40OLWMc6wkCMBOoya5su7JXH5iN7UR6kEWhpMMxoljVgdqomBoC7QkQAvD_BwE; _by_l_g_d=613e67e0-89aa-93b8-024a-7b92125df625; sensorsdata2015session=%7B%7D; au_id=6eb18c2776cc0019-7a45a2e11890f5d959079c; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2273637271%22%2C%22first_id%22%3A%221890f5fe3b04de-09270cb6534bec-3e604809-2073600-1890f5fe3b1b6c%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%2C%22_a_u_v%22%3A%220.0.6%22%2C%22utm_medium%22%3A%22affiliate%22%2C%22utm_source%22%3A%22%22%2C%22utm_campaign%22%3A%22%22%2C%22utm_term%22%3A%22%22%2C%22utm_content%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTg5MGY1ZmUzYjA0ZGUtMDkyNzBjYjY1MzRiZWMtM2U2MDQ4MDktMjA3MzYwMC0xODkwZjVmZTNiMWI2YyIsIiRpZGVudGl0eV9sb2dpbl9pZCI6IjczNjM3MjcxIn0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%2273637271%22%7D%2C%22%24device_id%22%3A%221890f5fe3b04de-09270cb6534bec-3e604809-2073600-1890f5fe3b1b6c%22%7D; LANG_KEY=zh-TW; REGION_ZO_REG_AFF={"utm_medium":"affiliate","lang":"zh-TW","g":"613e67e0-89aa-93b8-024a-7b92125df625","tdid":"a7c71ac4-1f8b-4afd-b2d0-b8d9455e273a","platform":"web","app_id":10006,"source":"zoomex.com","medium":"other","referrer":"www.zoomex.com/trade/usdt/BTCUSDT","url":"https://www.zoomex.com/zh-TW/login?redirect_url=https%3A%2F%2Fwww.zoomex.com%2Ftrade%2Fusdt%2FBTCUSDT"}; HMY_JC=9a24a54e684b2bd455352817b494d6322c720ba50aa2a3d3de0ead35f1fb9d3294,; HBB_HC=0041bbf0ad5a6ff6d39b8c10ec576ec039537a2f20156c36cfc2e68e5d1623f0e624bf00e42abe659c8c2b4e35b9b34b38; _gid=GA1.2.1790944097.1689505045; CSH_DF=fwUh5w7/+L0U1AT+EvTDcYtj6SSLXSXRLFq/sWlx+4zYTsQXzNziy5GVyYFOrcGDx4; CSH_UF=1062ce83a454d50e5d2c2a73c86fb978; secure-token=**************************************************************************************************************************************************************************************************************************************************************************************************************; b_t_c_k=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2ODk3NjQzMDIsInVzZXJfaWQiOjczNjM3MjcxLCJub25jZSI6IjdkZTE3ODIzIiwiZ2VuX3RzIjoxNjg5NTA1MTAyLCJwIjozLCJiIjoxLCJleHQiOnsiU3RhdGlvbi1UeXBlIjoiIiwibWN0IjoiMTY4ODk3NzI4MSJ9fQ.-7NeBa77q1cL_sFvad4r7OFWlj0H7M8eI46MNM6ZWkc; _ga_R0JK8HS939=GS1.1.1689505044.13.1.1689505103.0.0.0; _ga=GA1.1.1302026907.1688180089; _ga_6C3WHZC2VW=GS1.1.1689505044.13.1.1689505103.0.0.0; _ga_E1M05R283H=GS1.1.1689505044.13.1.1689505103.0.0.0; _ga_GMPG1FJ0LE=GS1.1.1689505044.8.1.1689505103.1.0.0	Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36',
        }
        
        apiKey = apiKey if apiKey else Api_Key
        self.ccyBi = ccyBi if ccyBi else CcyBi
        
        if apiKey in keys:
            apiKey = keys[apiKey].split('	') if type(keys[apiKey]) == str else keys[apiKey]
        else:
            apiKey = GetApiKey(apiKey, 'Zoomex')
            
        self.access_id = apiKey[0]
        self.secret_key = apiKey[1]
        self.passwd = apiKey[2]
        # print(self.access_id, self.secret_key, self.passwd)
        try:
            self.cookie = apiKey[3]
        except:
            self.cookie = 0

        if self.cookie:
            self.ua = apiKey[4]
        self.userToken = apiKey[0].split('b_t_c_k=')[-1].split(';')[0]


        self.HOST = 'https://api2.zoomex.com'

        self.session = requests.Session()

        """ 缓存费率数据"""
        self.feilvData = {}
        self.symbol = {}

        """ 多出口IP"""
        try:
            ips = DuoIp
            if len(ips) > 1:
                log('zoomex 使用多IP交易', ips)
        except:
            ips = []

        self.i = 0
        self.ips = []
        self.len_ips = len(ips)
        self.preRestTime = 0

        for ip in ips:
            from requests_toolbelt.adapters import source  # 指定出口IP
            sb = requests.Session()
            new_source = source.SourceAddressAdapter(ip)
            sb.mount('http://', new_source)
            sb.mount('https://', new_source)
            self.ips.append(sb)

    def dispatch_request(self, method):
        return {
            'GET': self.session.get,
            'DELETE': self.session.delete,
            'PUT': self.session.put,
            'POST': self.session.post,
        }.get(method, 'GET')

    # 请求API
    def go(self, method, path, payload=None, needKey=1, headers=0, url=''):

        url = self.HOST + path if not url else url

        if self.ips:
            self.session = self.ips[self.i]
            self.i += 1
            if self.i >= self.len_ips:
                self.i = 0

        
        headers = {
            "Access-Control-Allow-Credentials": "true",
            "Access-Control-Allow-Headers": "content-type,cookie,guid,lang,origin,refer,platform,usertoken,preflight,traceparent,requestId,datetime,x-client-tag,riskToken,station_type",
            "Access-Control-Allow-Methods": "GET,POST,PUT,DELETE,PATCH,HEAD,OPTIONS,CONNECT,TRACE",
            "Access-Control-Allow-Origin": "https://www.zoomex.com",
            "Access-Control-Expose-Headers": "token,content-length",
            "Content-Type": "application/json; charset=utf-8",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Origin": "https://www.zoomex.com",
            "Referer": "https://www.zoomex.com/",
            "Platform": "pc",
            "Cookie": self.cookie,
            "Usertoken": self.userToken,
        }

        if method == 'POST':
            params = {'url': url, 'timeout': 5, 'data': ujson.dumps(payload), 'headers': headers}
        else:
            params = {'url': url, 'timeout': 5, 'params': payload, 'headers': headers}

        if self.debug:
            t = NowTime_ms()

        # print(self.session.headers)
        try:
            response = self.dispatch_request(method)(**params)
            if response.text:
                response2 = response.json()
            else:
                response2 = ''

        except Exception as e:
            traceback.print_exc()

            print('请求API失败', url)
            time.sleep(1)
            return self.go(method, path, payload, needKey)

        if self.debug:
            yc = NowTime_ms() - t
            self.debugs.append(yc)
            print(path, str(yc) + 'ms', 'min:' + str(min(self.debugs)), 'max:' + str(max(self.debugs)),
                  '均:' + str(round(sum(self.debugs) / len(self.debugs), 2)))
        return response2

    """ 获取交易对规则"""

    def GetSymbols(self):
        parsed_data = self.go("GET", '/contract/v5/product/dynamic-symbol-list?filter=LinearPerpetual', {}, needKey=0)  # 修改 API 路径
        return parsed_data
        usdt_related = [item['symbolName'] for item in parsed_data["result"]["LinearPerpetual"] if item["quoteCurrency"] == "USDT"]
        return usdt_related

    """ 获取合约币种持仓"""
    def GetPos(self, symbol=0, all=0):
        params = {
            "_sp_business": "usdt",
            "timeStamp": int(time.time() * 1000)  # Generate a new timestamp
        }
        data = self.go('GET', f"/contract/v5/position/list-all", params)
        

        # print(data)
        try:
            if 'result' in data and 'ret_code' in data and data['ret_code'] == 0:
                okdata = data['result']
                data = []
                for v in okdata['list']:
                    symbol = v['data']['symbol']
                    if v['data']['fqX'] != '0':
                        ok = {}
                        ok['symbol'] = symbol
                        # print(v['data'])
                        ok['liang'] = abs(float(v['data']['fqX'])/10**8)
                        if not ok['liang']:
                            continue

                        ok['side'] = v['data']['side'].upper()
                        ok['side2'] = fanSide(ok['side'])
                        ok['jiage'] = float(v['data']['entryPriceE8'])/10**8
                        ok['nowJiage'] = float(v['data']['markPrice'])
                        ok['yingkui'] = float(v['data']['unrealisedPnlE8'])/10**8
                        ok['bzj'] = 0
                        ok['roe'] = 0
                        ok['time'] = NowTime_ms()

                        data.append(ok)

                return data

            else:
                log(Color("获取现货持仓失败", -1), data)
                time.sleep(3)
                try:
                    print(Seting)
                except:
                    time.sleep(60)
                    return []
                return self.GetPos(symbol, all)

        except Exception as e:
            uploadError(traceback.format_exc())

        log(Color("获取现货持仓失败", -1), data)
        uploadLog(isExit=1)
        os._exit(0)


    """下单"""
    def PostOrder(self, symbol, side, jiage, liang, type='ioc', jiancang=0, msg2=''):

        liang2 = str(liang) + ' (' + STR_N(float(liang) * float(jiage)) + '$)'
        jiage = str(jiage)
        liang = str(int(liang*10**8))

        post = {
            "basePrice": jiage,
            "closeOnTrigger": False if not jiancang else True,
            "leverageE2": "2000",
            "orderType": "Limit" if type == 'ioc' else "Market",
            "positionIdx": 0,
            "price": jiage,
            "qtyX": liang,
            "reduceOnly": False,
            "side": "Buy" if side == 'BUY' else "Sell",
            "slTriggerBy": "LastPrice",
            "symbol": symbol,
            "timeInForce": "ImmediateOrCancel" if type == 'ioc' else "GoodTillCancel",
            "tpTriggerBy": "LastPrice",
            "type": "Activity",
            "triggerBy": "LastPrice",
            "triggerPrice": "",
            "action": "Open" if not jiancang else 'PositionClose',
            "qtyType": 0,
            "qtyTypeValue": 0,
            "preCreateId": "",
        }

        msg = 'Zoomex ' + symbol + ' '

        if jiancang:
            msg += '平空仓' if side == 'BUY' else '平多仓'
            # post["side"] = "Sell" if side == 'BUY' else "Buy"
        else:
            msg += '开多仓' if side == 'BUY' else '开空仓'

        msg += "　方向:" + post["side"] + "　价格:" + jiage + "　量:" + liang2 + "　减仓:" + str(jiancang) + "　Type:" + type

        orderId = 0

        t = NowTime_ms()

        for x in range(1):
            response_dict = self.go('POST', "/v3/linear/private/order/create", payload=post)

            if 'ret_code' not in response_dict or response_dict['ret_code'] != 0:
                log(symbol + " [!!!] 下单失败！！重试", post, response_dict)
            else:
                orderId = response_dict['result']['orderId']
                break

        msg = [msg + '　' + Color(msg2, 1), str(NowTime_ms() - t) + 'ms', orderId]

        return orderId, msg

    """获取可用余额"""
    def GetYuer(self, p=1, ccyBi=0, symbols={}):
        ccyBi = ccyBi if ccyBi else self.ccyBi
        data = self.go("GET", "/v3/private/wallet/list")
        if not data or not data['result']:
            log('获取余额失败', data)
            try:
                print(Seting)
            except:
                time.sleep(60)
                return {'all': 0.01, 'keyong': 0.01, 'yingkui': 0.01}
            time.sleep(5)
            return self.GetYuer(p, ccyBi, symbols)
        # print("data=%s"%data)
        no = 0
        okk = 0
        for v in data['result']['list']:
            if v['data']['coin'] == ccyBi:  # USDT
                okk = v
                break
        if not okk:
            no = 1

        if no:
            print('zoomex 获取余额失败', data)
            time.sleep(1)
            return self.GetYuer(p)

        ok = {}
        ok['all'] = Si(int(okk['data']['equityE8'])/10**8, 4)
        ok['keyong'] = Si(int(okk['data']['afterCrossAbE8'])/10**8, 4)
        ok['yingkui'] = N(int(okk['data']['totalPositionBalanceE8'])/10**8, 4)

        ok['all'] = ok['all'] if ok['all'] else 0.01
        ok['keyong'] = ok['keyong'] if ok['keyong'] else 0.01

        if p:
            log(p + '  zoomex USDT总余额' if type(p) == str else 'zoomex USDT总余额', ok['all'], '可用', N(ok['keyong'], 4),
                '持仓盈亏', N(ok['yingkui'], 4))

        return ok

    def GetWsUrl(self, auth=0):
        if auth:
            url = '/api/v1/bullet-private'
        else:
            url = '/api/v1/bullet-public'

        res = self.go("POST", url, {})

        if res["code"] == "200000":
            token = res["data"]["token"]
            ws_connect_id = str(uuid4()).replace('-', '')
            endpoint = res["data"]['instanceServers'][0]['endpoint']
            ws_endpoint = f"{endpoint}?token={token}&connectId={ws_connect_id}"
            encrypt = res["data"]['instanceServers'][0]['encrypt']
            if auth:
                ws_endpoint += '&acceptUserMessage=true'

            return ws_endpoint
        else:
            raise Exception("kucoin spot 获取token错误")

    
    """设置杠杆倍数"""
    def SetGangGan(self, symbol, beishu):
        beishu = int(beishu)*10
        fh = self.go("POST","/v3/linear/private/position/set-leverage",{'symbol': symbol,'buyLeverageE2': beishu, 'sellLeverageE2': beishu, 'skipSwitchIsolated': True, 'positionIdx': "0"})
        log('Zoomex', symbol, '设置杠杠', beishu, fh)
        return fh



if __name__ == "__main__":
    k = zoomex('TEST')
    balance = k.GetYuer()
    print("当前获取现货余额：%s" % balance)
    symbols = k.GetSymbols()
    print("市场现货列表：%s" % symbols[:5])
    hold = k.GetPos()
    print("合约持仓：%s" % hold)
    # order = k.PostOrder('BTCUSDT', 'BUY', 30722.9, 0.005, 'ioc', jiancang=0)
    print("下单：%s" % str(order))
