#!/usr/bin/python
# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from urllib import request
import urllib3
from urllib3.exceptions import InsecureRequestWarning

urllib3.disable_warnings(InsecureRequestWarning)
http = urllib3.PoolManager(timeout=urllib3.Timeout(connect=1, read=2))

import hmac
import hashlib
import requests
import datetime
import time
import json
import traceback        #输出报错
from urllib.parse import parse_qs, urlencode

from main.config import *
from main.hanshu import *
from exchangeWs.okx_ws_trade import okx_ws_trade


def get_sign(message, secret_key):
    mac = hmac.new(bytes(secret_key, encoding='utf8'), bytes(message, encoding='utf-8'), digestmod='sha256')
    d = mac.digest()
    return base64.b64encode(d)

def pre_hash(timestamp, method, request_path, body):
    return str(timestamp) + str.upper(method) + request_path + body

def parse_params_to_str(params):
    url = '?'
    for key, value in params.items():
        url = url + str(key) + '=' + str(value) + '&'

    return url[0:-1]

def get_timestamp():
    now = datetime.datetime.utcnow()
    t = now.isoformat("T", "milliseconds")
    return t + "Z"


class okx():

    def __init__(self, apiKey=0, ccyBi=0, symbols={}):
        keys = {
            '母账号': ['fb7bf262-5080-44d3-ad86-7a33ac9584be','46FBC6884E1838C3FBA0D20F60A80F97', 'Kekenb888!@#'],
            '小号': ['8231bad8-856e-40ad-ab7a-52f4315d0266','C2CBD257FD792457AFF9303425E7D12C', 'Kekenb888!@#'],
            'TEST': ['74b7659d-dd5b-4ef3-98ea-0e0e62ba481c','9E6562765125E733066EE50019C07B45', 'Test12345@'],
            'ltokemaintain': ['f142593e-5319-48c7-96d4-e8c018115275','DC19CAB7464ED007F16D702F47AD3E3B', 'showmethemoney'],

            'Robin母': ['d190ec2a-c9cc-4412-ad16-ee199246e74c','9795B086261D45356E99FC6ACF5BE159', 'Test12345@'],

            'Robin1': ['5f3e4c9f-c587-4fdb-b09f-5b6f675c2116','7DF2DB4821401CA9748319875DCEE410', 'Test12345@'],
            'Robin2': ['6f5400a7-7be9-467d-8a11-1faf1377c081','4576B6ADE308D8C54D90447CB43FA4E5', 'Test12345@'],
            'Robin3': ['2b1edeb0-6a64-47ff-b3f0-2881751e0df0','98A57C09BF24E17D67A77E02C819BBF1', 'Test12345@'],
            'Robin4': ['557ef394-c4e6-4f24-ac68-de89ccb9f214','75E0BB1F20B12C48AEBB54D35BE2E73A', 'Test12345@'],
            'Robin5': ['9743f990-133b-4cce-aae5-bd9ec1aacb27','0CD530F1279EB3BD7580BFA18D0239AE', 'Test12345@'],
            'Robin6': ['e5dd8d61-497f-4a84-8650-9f69640c8163','07EA278C3AEDB6A87569BC44E68EF2FF', 'Test12345@'],
            'Robin7': ['30966edb-9971-4a1c-b02c-d09d37f5dc49','D0ED52EBFAA02C93B62340C81F05696D', 'Test12345@'], #testtest7
            'Robin8': ['8d396aa5-a8ed-477a-be3f-aa531063c9e5','091EDEEF0626E4E65F30BFEFA14FBDD8', 'Test12345@'], #testtest8
            'Robin9': ['273664c4-bb20-40a1-83b9-1117d3c5b8e9','5B63C18BFAC8E0CF095FF89BA2DD10E9', 'Test12345@'], #testtest9

            '市商': ['f142593e-5319-48c7-96d4-e8c018115275','DC19CAB7464ED007F16D702F47AD3E3B', 'showmethemoney'], #testtest9
            'ltp': ['963e4c37-1093-457a-be75-d7a33121859c','4FC73AE81BA88D3DEDDD1719E0F2B126', 'Qwer123456@'], #testtest9

            '吃了吗资本': ['e9a58673-038b-4002-bea4-3042aa3bbc71','C6C03FEFB346F81D6AA8194E800AAA67', 'Test666@@@'], #testtest9

        }

        self.debug = 0
        self.debugs = []
        self.symbol = symbols
        self.tickerData = 0
        self.ccyBi = ccyBi if ccyBi else CcyBi

        apiKey = apiKey if apiKey else Api_Key2
        self.access_id = keys[apiKey][0]
        self.secret_key = keys[apiKey][1]
        self.passwd = keys[apiKey][2]

        self.url = 'https://www.okx.com'
        self.session = requests.Session()
        
        """ 缓存费率数据"""
        self.feilvData = {}

        if '均衡' not in ServerName:
            self.SetChicang('long_short_mode')
            self.ws_trade = okx_ws_trade(self)

    
    def dispatch_request(self, http_method):
        return {
            'GET': self.session.get,
            'DELETE': self.session.delete,
            'PUT': self.session.put,
            'POST': self.session.post,
        }.get(http_method, 'GET')


    def set_authorization(self, timestamp, sgin):
        self.session.headers = {
            'Content-Type': 'application/json',
            'OK-ACCESS-KEY': self.access_id,
            'OK-ACCESS-SIGN': sgin,
            'OK-ACCESS-TIMESTAMP': str(timestamp),
            'OK-ACCESS-PASSPHRASE': self.passwd,
        }


    # 请求API
    def go(self, http_method, url_path, payload={},needKey=1):
        
        if http_method == 'GET':
            request_path = url_path + parse_params_to_str(payload)
        else:
            request_path = url_path

        # url
        url = self.url + request_path
        timestamp = get_timestamp()

        body = json.dumps(payload) if http_method == 'POST' else ""
        sign = get_sign(pre_hash(timestamp, http_method, request_path, str(body)), self.secret_key)

        self.set_authorization(timestamp, sign)

        # if body:
        #     print("url:", url)
        #     print("body:", body)

        
        params = {'url': url, 'timeout': 5}

        if http_method == 'POST':
            params['data'] = body


        if self.debug:
            t = NowTime_ms()

        # print(self.session.headers)
        try:
            response = self.dispatch_request(http_method)(**params)
            if response.text:
                response2 = response.json()
            else:
                response2 = ''

        except Exception as e:
            traceback.print_exc()

            print('请求API失败',url2)
            time.sleep(1)
            return self.go(http_method,url_path, payload,url,needKey)

        if self.debug:
            yc = NowTime_ms() - t
            self.debugs.append(yc)
            print(url_path, payload, str(yc)+'ms', 'min:'+str(min(self.debugs)), 'max:'+str(max(self.debugs)), '均:'+str(round(sum(self.debugs)/len(self.debugs),2)))

        return response2

    """最大杠杆"""
    def GetMaxGangGan(self, symbol):
        fh = self.go('GET', "/api/v5/account/adjust-leverage-info", {'instType': 'SWAP', 'mgnMode': 'cross', 'lever': 1, 'instId': symbol})['data'][0]['maxLever']
        log(f'Okx {symbol} 最大杠杠', fh)
        return int(fh)


    """ 获取盘口挂单"""
    def GetDepth(self, symbol, limit=1):
        params = {'instId': symbol}
        if limit:
            params['sz'] = limit

        data = self.go('GET','/api/v5/market/books', params)
        if 'data' not in data or not len(data['data']):
            log(Color(symbol+' 获取深度失败', -1), data)
            time.sleep(0.01)
            return self.GetDepth(symbol)
        data = data['data'][0]
        
        return data


    """获取可用余额"""
    def GetYuer(self, p=1):
        params = {'ccy': "USDT"}
        data = self.go("GET","/api/v5/account/balance", params)

        no = 0
        ok = 0
        if 'data' in data:
            for v in data['data']:
                try:
                    ok = v['details'][0]
                except:
                    log('okx没u', data)
                    ok = {
                        'cashBal': 0.01,
                        'availEq': 0.01,
                        'upl': 0,
                    }
                break

        if not ok:
            no = 1

        if no:
            print('Okx获取余额失败', data)
            time.sleep(1)
            return self.GetYuer(p)

        
        if ok['availEq'] == '':
            logWait("Okx合约未开通")

        ok['all'] = Si(float(ok['cashBal']) + float(ok['upl']), 4)
        ok['all'] = max(ok['all'], 0.01)
        ok['keyong'] = N(float(ok['availEq']), 4)
        ok['yingkui'] = N(float(ok['upl']), 4)
        if p:
            log(p+'  OKX USDT总余额' if type(p) == str else 'OKX USDT总余额', ok['all'], '可用', ok['keyong'], '持仓盈亏', ok['yingkui'])

        return ok


    """设置单项持仓"""
    def SetChicang(self, posMode='net_mode'): #long_short_mode
        fh = self.go('POST', "/api/v5/account/set-position-mode", {'posMode': posMode})
        log('OKX设置持仓', '单向' if posMode == 'net_mode' else '双向', fh)


    """获取所有交易对信息"""
    def GetSymbols(self, instType='SWAP', jiaoyi=0):
        params = {'instType': instType}
        if jiaoyi:
            return self.go('GET', '/api/v5/market/tickers', params)['data']
        else:
            return self.go('GET', '/api/v5/public/instruments', params)['data']


    """ 获取费率"""
    def GetSymbolFeilv(self, symbol):
        if symbol not in self.feilvData.keys():
            data = self.go("GET","/api/v5/public/funding-rate", {'instId': symbol})

            if not data or 'data' not in data:
                print('获取资金失败', data)
                time.sleep(0.1)
                return self.GetSymbolFeilv(symbol)

            self.feilvData[symbol] = data['data'][0]

        symbolData = self.feilvData[symbol]
        if not symbolData or NowTime_ms() >= float(symbolData['nextFundingTime'])+2000:
            log(symbol, '更新资金费率',
                 '更新时间', GetTime(symbolData['nextFundingTime'], '%m-%d %H:%M:%S') if symbolData else '',
                 '获取时间', GetTime(symbolData['fundingTime'], '%m-%d %H:%M:%S') if symbolData else ''
                 )

            self.feilvData = {}
            return self.GetSymbolFeilv(symbol)

        else:
            # print(NowTime_ms(), symbolData['nextFundingTime'])
            # print(symbol, '获取资金费率',
            #      '更新时间', GetTime(symbolData['nextFundingTime'], '%m-%d %H:%M:%S') if symbolData else '',
            #      '获取时间', GetTime(symbolData['time'], '%m-%d %H:%M:%S') if symbolData else ''
            #      )
            return round(float(symbolData['fundingRate'])*100, 4)



    """设置杠杆倍数和持仓方向"""
    def SetGangGan(self, symbol, beishu):
        """
        long：双向持仓多头
        short：双向持仓空头
        net：单向持仓
        """
        if not beishu:
            return
        post = {
            'instId': symbol,
            'lever': str(beishu),
            'mgnMode': 'cross', #全仓模式
        }
        fh = self.go('POST', "/api/v5/account/set-leverage", post)
        log(symbol, '设置杠杠', beishu, fh)
        time.sleep(0.1)
        return
        if 'code' not in fh or fh['code'] != '0':
            return self.SetGangGan(symbol, beishu)



    """ 获取所有交易对价格、挂单盘口"""
    def GetTickerAll(self):
        data = self.go("GET","/api/v5/market/tickers", {'instType': 'SWAP'})

        try:
            return data['data']
        
        except:
            print("OKX 获取ticker出错")
            return {}


    """ 获取所有持仓"""
    def GetPos(self,):
        
        data = self.go('GET', '/api/v5/account/positions', {})

        try:
            if data['code'] == '0' and 'data' in data:
                okdata = data['data']
                data = []
                for v in okdata:
                    ok = {}
                    ok['symbol'] = v['instId']
                    ok['liang'] = abs(float(v['pos']))
                    
                    if v['posSide'] == 'net':
                        if "SWAP" in ok['symbol']:
                            ok['side'] = 'BUY' if float(v['pos']) > 0 else 'SELL' #持仓方向
                        else:
                            ok['side'] = 'SELL' if 'USDT' == v['posCcy'] else 'BUY' #持仓方向

                    else:
                        ok['side'] = 'BUY' if v['posSide'] == 'long' else 'SELL' #持仓方向

                    ok['side2'] = 'BUY' if ok['side'] == 'SELL' else 'SELL'
                    ok['jiage'] = float(v['avgPx'])
                    ok['create_time'] = float(v['cTime'])
                    ok['nowJiage'] = float(v['last'])    #最新成交价格
                    ok['yingkui'] = N(float(v['upl']), 4)
                    ok['bzj'] = N(float(v['imr']), 4)
                    ok['roe'] = N(float(v['uplRatio']) * 100,2)
                    ok['time'] = NowTime_ms()
                    data.append(ok)

                return data

        except Exception as e:
            uploadError(traceback.format_exc())
        
        log(Color("Okx 获取持仓失败", -1), data)
        uploadLog(isExit=1)
        os._exit(0)


    """下单"""
    def PostOrder(self, symbol, side, jiage, liang, type='ioc', jiancang=0, msg2=''):
        if not self.ws_trade.symbol:
            self.ws_trade.symbol = self.symbol

        return self.ws_trade.PostOrder(symbol, side, jiage, liang, type, jiancang, msg2)  #使用ws下单可以加速10-20ms，减少网络波动
    
        MZ = self.symbol[symbol]['Z'] if symbol in self.symbol else 1
        liang2 = str(liang)+' ('+STR_N(float(liang)*float(jiage)*MZ)+'$)'

        jiage = str(jiage)
        liang = str(int(liang))

        #type = 'post_only'     #只挂单
        posSide = 0     #a兼容策略处理

        msg = 'OKX　'+symbol+' 　'

        if jiancang or (posSide == 'long' and side == 'SELL') or (posSide == 'short' and side == 'BUY'):
            msg += '平空仓' if side == 'BUY' else '平多仓'
        else:
            msg += '开多仓' if side == 'BUY' else '开空仓'

        if posSide:
            msg += '　持仓:'+posSide

        if type == 'normal':
            type = 'market'

        msg += "　方向:"+side+"　价格:"+jiage+"　量:"+liang2+"　面值:"+str(MZ)+"　减仓:"+str(jiancang)+"　Type:"+type


        post = {
            'ccy': self.ccyBi,
            'tdMode': 'cross',      #保证金模式：全仓
            'instId': symbol,  #交易对
            'side': side.lower(),           #方向
            'ordType': type,        #模式
            'sz': liang,         #数量
        }
        if posSide:
            post['posSide'] = posSide.lower()

        if type != 'market' and type != 'optimal_limit_ioc':
            post['px'] = jiage
            
        if jiancang:
            post['reduceOnly'] = True

        orderId = 0
        
        t = NowTime_ms()

        for x in range(1):
            order = self.go('POST',"/api/v5/trade/order",post)
            # log("下单返回", order)
            if not len(order['data']) or not 'ordId' in order['data'][0].keys():
                uploadError("OKX下单失败："+str(order))
                
            else:
                orderId = order['data'][0]['ordId']
                break
        
        msg = [msg+ '　'+Color(msg2, 1), str(NowTime_ms()-t)+'ms', orderId]
        
        return orderId, msg


    """ 全部撤单"""
    def DeleteAllOrder(self, symbol):
        t = NowTime_ms()
        orders = self.go('GET', '/api/v5/trade/orders-pending', {'instType': 'SWAP', 'instId': symbol})

        ids = []
        msg = ''
        if 'data' in orders and orders['data']:
            for data in orders['data']:
                ids.append({'instId': data['instId'], 'ordId': data['ordId']})

            msg = self.go("POST","/api/v5/trade/cancel-batch-orders", ids )

        log(symbol, "撤销所有挂单", ids, msg, str(NowTime_ms()-t)+'ms')


    """市价平仓"""
    def ClosePos(self, symbol, liang=0, jiage=0, side=''):

        t = NowTime_ms()
        
        params = {
            'instId': symbol,
            'mgnMode': 'cross',
        }
        fh = self.go('POST', "/api/v5/trade/close-position", params)
        print(fh)

        log("OKX", Color("TakerClose 套利单腿成交", -1), side, symbol,
         "价格", jiage, "量", str(liang)+' ('+STR_N(liang*jiage)+'$)', str(NowTime_ms()-t)+'ms', fh['data'])

    """ 获取最大开仓张"""
    def GetMaxPos(self, symbol):

        if not self.tickerData or tlog('OkxTickers', '', 30, xs=0):
            self.tickerData = self.go('GET', "/api/v5/market/tickers", {'instType': 'SWAP'})['data']

        params = {
            'instId': symbol,
            'tdMode': 'cross',
        }
        fh = self.go('GET', "/api/v5/account/max-size", params)
        m = float(min(fh['data'][0]['maxBuy'], fh['data'][0]['maxSell']))
        price = 0
        for v in self.tickerData:
            if v['instId'] == symbol:
                price = float(v['bidPx'])
                break
        
        else:
            log('Okx', '未找到交易对价格', symbol)
            m = 0

        time.sleep(0.3)
        return m, price


    """获取现货余额"""
    def GetXianhuo(self, bi=''):
        bi = bi if bi else CcyBi
        data = self.go("GET", "/api/v5/asset/balances", {'ccy':bi})
        return Si(data['data'][0]['availBal'], 4)

    
    """ 币种列表"""
    def currencies(self, bi='', net=''):
        bi = bi if bi else self.ccyBi
        post = {
            'ccy':bi,
        }
        data = self.go("GET", "/api/v5/asset/currencies", post)
        if 'data' in data and data['data']:
            if net:
                for v in data['data']:
                    if net in v['chain']:
                        return Si(float(v['wdQuota'])-float(v['usedWdQuota']), 4), v['minFee']
            else:
                return data['data']
        else:
            return 0, 0

    
    """提现"""
    def Tixian(self, dizhi, liang, bi='', net='TRC20'):
        bi = bi if bi else self.ccyBi

        
        edu, fee = self.currencies(bi, net)
        if float(liang) > edu:
            return log('Okx提现 额度不足', dizhi, '数量',liang, '币种', bi, '链', net, '<', '额度', edu)

        net = bi+'-'+net
        post = {
            'ccy':bi,
            'amt': liang, 
            'dest': 4,
            'toAddr': dizhi,
            'fee': fee,
            'chain': net
        }
        data = self.go("POST", "/api/v5/asset/withdrawal", post)
        
        log('Okx提现 地址', dizhi, '数量',liang, '币种', bi, '手续费', fee, '链', net, '结果', data)
        return data

    
    """ 子母账户相互转账"""
    def ziHua(self, liang, ziName, side, bi=''):
        bi = bi if bi else self.ccyBi
        data = {
            'ccy': bi, 
            'amt': str(liang),
            'subAcct': ziName,
        }

        """ 转入子账户
        6：资金账户 18：交易账户
        0：账户内划转
        1：母账户转子账户(仅适用于母账户APIKey)
        2：子账户转母账户(仅适用于母账户APIKey)
        3：子账户转母账户(仅适用于子账户APIKey)
        4：子账户转子账户(仅适用于子账户APIKey，且目标账户需要是同一母账户下的其他子账户)
        默认是0
        """
        if side == 'in':
            data['from'] = '6'
            data['to'] = '18'
            data['type'] = '1'

        else:
            data['from'] = '18'
            data['to'] = '6'
            data['type'] = '2'

        # pprint(data)
        data = self.go("POST", "/api/v5/asset/transfer", data)
        log('[Okx万向划转]', ziName, '转入子账户' if side == 'in' else '转出子账户', '币种', bi, liang, '结果', data, Color('', 1))
        time.sleep(1)
        return data['data']


    """ 获取充值地址"""
    def GetAddrees(self, bi=''):
        bi = bi if bi else self.ccyBi
        data = self.go('GET', '/api/v5/asset/deposit-address', {'ccy': bi})
        return data
    
    """ 创建子账户"""
    def CreateUser(self, name, msg=''):    
        p = {
            'subAcct': name,
            'label': msg,
        }

        fh = self.go("POST", "/api/v5/broker/nd/create-subaccount", p)
        log('okx创建子账户', name, msg, '返回', fh)
        return fh