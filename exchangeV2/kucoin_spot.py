#!/usr/bin/python
# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from urllib import request
import urllib3
import urllib
from urllib3.exceptions import InsecureRequestWarning

urllib3.disable_warnings(InsecureRequestWarning)
http = urllib3.PoolManager(timeout=urllib3.Timeout(connect=1, read=2))

import time
import ujson
import traceback        #输出报错
from pprint import pprint

import requests
from urllib import parse
import json
from datetime import datetime
import hmac
import base64
import hashlib
from uuid import uuid4


from main.config import *
from main.hanshu import *

def parse_params_to_str(params):
    url = '?'
    for key, value in params.items():
        url = url + str(key) + '=' + str(value) + '&'
    return url[0:-1]

class kucoin_spot():
    def __init__(self, apiKey='', ccyBi=0, symbols={}):
        keys = {
            '吃了吗资本Spot': ['638b669589f46c0001d7cda0', 'a49cf518-e6ba-48bf-a778-6bb946fe3830', 'b87d055f'],
            'richrich1': ['6461fd439a3a540001fa6c36', '4d9a651a-002b-4a56-96a9-f838e4ab9271', 'b87d055f'],
            'richrich2': ['6393f35496c8b0000128750b', 'b0456d83-ba0d-4809-a13c-8d755ce85ffc', 'b87d055f'],
        }
        self.debug = 0
        self.debugs = []
        self.tickerData = 0
        self.ccyBi = ccyBi if ccyBi else CcyBi2
        apiKey = apiKey if apiKey else Api_Key2
        self.access_id = keys[apiKey][0]
        self.secret_key = keys[apiKey][1]
        self.passwd = keys[apiKey][2]
        self.symbol = {}

        self.session = requests.Session()

        """ 多出口IP"""
        try:
            ips = DuoIp
            if len(ips) > 1:
                log(f'Kucoin 使用多IP交易', ips)
        except:
            ips = []

        self.i = 0
        self.ips = []
        self.len_ips = len(ips)
        for ip in ips:
            from requests_toolbelt.adapters import source  # 指定出口IP
            sb = requests.Session()
            new_source = source.SourceAddressAdapter(ip)
            sb.mount('http://', new_source)
            sb.mount('https://', new_source)
            self.ips.append(sb)

    def dispatch_request(self, method):
        return {
            'GET': self.session.get,
            'DELETE': self.session.delete,
            'PUT': self.session.put,
            'POST': self.session.post,
        }.get(method, 'GET')

    def get_sign(self, timestamp, method, request_path, params, secret_key):
        if params == None:
            params_str = ""
        else:
            if method == 'POST':
                params_str = ujson.dumps(params)
            else:
                params_str = parse_params_to_str(params)

        message = str(timestamp) + str.upper(method) + request_path + params_str
        mac = hmac.new(bytes(secret_key, encoding='utf-8'), bytes(message, encoding='utf-8'),
                       digestmod='sha256').digest()
        return str(base64.b64encode(mac), 'utf-8')

    # 请求API
    def go(self, method, path, payload=None, needKey=1, spot=1):  # 将 spot 默认值改为 1
        if spot:
            url = "https://api.kucoin.com" + path

        else:
            url = "https://api-futures.kucoin.com" + path

        if self.ips:
            self.session = self.ips[self.i]
            self.i += 1
            if self.i >= self.len_ips:
                self.i = 0

        headers = {}
        if needKey:
            now_time = int(time.time()) * 1000
            str_to_sign = str(now_time) + method + path
            if method in ['GET', 'DELETE']:
                data_json = ''
                if payload:
                    strl = []
                    for key in payload:
                        strl.append("{}={}".format(key, payload[key]))
                    data_json += '&'.join(strl)
                    str_to_sign += '?' + data_json
            elif payload:
                str_to_sign += ujson.dumps(payload)

            sign = base64.b64encode(
                hmac.new(self.secret_key.encode('utf-8'), str_to_sign.encode('utf-8'), hashlib.sha256).digest())
            passphrase = base64.b64encode(
                hmac.new(self.secret_key.encode('utf-8'), self.passwd.encode('utf-8'), hashlib.sha256).digest())
            headers = {
                "KC-API-SIGN": sign.decode(),
                "KC-API-TIMESTAMP": str(now_time),
                "KC-API-KEY": self.access_id,
                "KC-API-PASSPHRASE": passphrase.decode(),
                "Content-Type": "application/json",
                "KC-API-KEY-VERSION": "2"
            }

        headers["User-Agent"] = "kucoin-python-sdk/v1.0"

        if method == 'POST':
            params = {'url': url, 'timeout': 5, 'headers': headers}
            if payload:
                params['data'] = ujson.dumps(payload)
        else:
            params = {'url': url, 'timeout': 5, 'params': payload, 'headers': headers}

        if self.debug:
            t = NowTime_ms()

        # print(params)
        try:
            response = self.dispatch_request(method)(**params)
            if response.text:
                response2 = response.json()
            else:
                response2 = ''

        except Exception as e:
            traceback.print_exc()

            print('请求API失败', url)
            time.sleep(1)
            return self.go(method, path, payload, needKey)

        if self.debug:
            yc = NowTime_ms() - t
            self.debugs.append(yc)
            print(path, str(yc) + 'ms', 'min:' + str(min(self.debugs)), 'max:' + str(max(self.debugs)),
                  '均:' + str(round(sum(self.debugs) / len(self.debugs), 2)))

        return response2

    """ 获取交易对规则"""
    def GetSymbols(self):
        return self.go("GET", '/api/v2/symbols', {}, needKey=0)['data']  # 修改 API 路径

    """ 获取现货币种持仓"""

    def GetPos(self, symbol=0, all=0):
        data = self.go('GET', f"/api/v1/accounts", {}, spot=1)
        try:
            if 'data' in data and 'code' in data and data['code'] == '200000':
                okdata = data['data']
                data = []
                for v in okdata:
                    symbol = v['currency']
                    if symbol == CcyBi2 and not all:
                        continue

                    if v['type'] == 'trade_hf':    #trade
                        ok = {}
                        ok['symbol'] = symbol + '-' + self.ccyBi if symbol != self.ccyBi else symbol
                        ok['liang'] = float(v['available'])
                        if not ok['liang']:
                            continue

                        ok['side'] = 'BUY'
                        ok['side2'] = 'SELL'
                        ok['jiage'] = 0
                        ok['nowJiage'] = 0
                        ok['yingkui'] = 0
                        ok['bzj'] = 0
                        ok['roe'] = 0
                        ok['time'] = NowTime_ms()

                        data.append(ok)

                return data

            else:
                log(Color("获取现货持仓失败", -1), data)
                time.sleep(3)
                return self.GetPos(symbol, all)

        except Exception as e:
            uploadError(traceback.format_exc())

        log(Color("获取现货持仓失败", -1), data)
        uploadLog(isExit=1)
        os._exit(0)

    """现货下单"""
    def PostOrder(self, symbol, side, jiage, liang,  type='ioc', jiancang=0, msg2=''):
        jiage = str(jiage)
        liang = str(liang)
        liang2 = liang+' ('+STR_N(float(liang)*float(jiage))+'$)'

        msg = 'Kucoin　' + symbol + '　'
        msg += '买入' if side == 'BUY' else '卖出'
        msg += "　方向:" + side + "　价格:" + jiage + "　量:" + liang2 + "　Type:" + type

        post = {
            'clientOid': str(uuid4()),
            'side': side.lower(),
            'symbol': symbol,
            'price': jiage,
            'size': liang,
        }
        if type == 'ioc':
            post['type'] = 'limit'
            post['timeInForce'] = 'IOC'
        else:
            post['type'] = 'market'
            post['timeInForce'] = 'GTC'


        orderId = 0

        t = NowTime_ms()

        for x in range(1):
            # order = self.go('POST', "/api/v1/orders", post, spot=1)
            order = self.go('POST', "/api/v1/hf/orders", post, spot=1)

            if not order or 'data' not in order or not order['data'] or 'orderId' not in order['data']:
                log(symbol + " [!!!] 现货下单失败！！重试", post, order)
            else:
                orderId = order['data']['orderId']
                break

        msg = [msg + '　' + Color(msg2, 1), str(NowTime_ms() - t) + 'ms', orderId]

        return orderId, msg


    """获取现货账户u的余额"""
    def GetYuer(self, p=1, ref=1, ccyBi=0, symbols={}):
        ccyBi = ccyBi if ccyBi else CcyBi2

        all = self.GetPos(all=1)
        if not self.tickerData or tlog('KucoinSpotTickers', '', 20, xs=0):
            self.tickerData = self.go('GET', "/api/v1/market/allTickers")['data']['ticker']

        keyong = 0
        jiazhi = 0
        for pos in all:
            if pos['symbol'] == ccyBi:
                keyong = pos['liang']

            else:
                for item in self.tickerData:
                    if pos['symbol'] == item['symbol']:
                        jiazhi += pos['liang'] * float(item['sell'])
                        break

                else:
                    if pos['symbol'] not in ['USDTUSDT', 'USDCUSDT'] and '均衡' not in ServerName:
                        log(pos, 'Kucoin Spot未找到价格', Color('', -1))

        ok = {}
        ok['all'] = Si(jiazhi + keyong, 4)
        ok['keyong'] = Si(keyong, 4)
        ok['yingkui'] = 0

        ok['all'] = ok['all'] if ok['all'] else 0.01
        ok['keyong'] = ok['keyong'] if ok['keyong'] else 0.01

        if p:
            log('KucoinSpot USDT总余额', ok['all'], '可用', N(ok['keyong'], 4),
                '持仓盈亏', N(ok['yingkui'], 4))

        return ok

    def GetWsUrl(self, auth=0):
        if auth:
            url = '/api/v1/bullet-private'
        else:
            url = '/api/v1/bullet-public'

        res = self.go("POST", url, {})

        if res["code"] == "200000":
            token = res["data"]["token"]
            ws_connect_id = str(uuid4()).replace('-', '')
            endpoint = res["data"]['instanceServers'][0]['endpoint']
            ws_endpoint = f"{endpoint}?token={token}&connectId={ws_connect_id}"
            encrypt = res["data"]['instanceServers'][0]['encrypt']
            if auth:
                ws_endpoint += '&acceptUserMessage=true'

            return ws_endpoint
        else:
            raise Exception("kucoin spot 获取token错误")


    """ 高频和现货账户互转"""
    def Huazhuan_gp(self, ccyBi, liang, side='in'):
        ccyBi = ccyBi if ccyBi else CcyBi2
        data = {
            'clientOid': str(uuid4()),
            'currency': ccyBi,
            'amount': liang,
        }
        if side == 'in':
            data['from'] = 'main'
            data['to'] = 'trade_hf'
        else:
            data['to'] = 'main'
            data['from'] = 'trade_hf'
        
        data = self.go("POST", "/api/v2/accounts/inner-transfer", data)
        log('[Kucoin现货高频]', side, '币种', ccyBi, liang, '结果', data, Color('', 1))
        return data


if __name__ == "__main__":
    k = kucoin_spot()
    balance = k.GetYuer()
    print("当前获取现货余额：%s"%balance)
    symbols = k.GetSymbols()
    print("市场现货列表：%s"%symbols[:5])
    hold = k.GetPos('doge')
    print("现货持仓：%s"%hold)
    order = k.PostOrder('DOGE-USDT', 'BUY', 0.0728, 3)
    print("下单：%s"%str(order))
