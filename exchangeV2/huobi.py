#!/usr/bin/python
# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from urllib import request
import urllib3
import urllib
from urllib3.exceptions import InsecureRequestWarning

urllib3.disable_warnings(InsecureRequestWarning)
http = urllib3.PoolManager(timeout=urllib3.Timeout(connect=1, read=2))

import time
import ujson
import traceback        #输出报错
from pprint import pprint

# import requests

try:
    import rusthttp_py.adapter as requests
    print('使用RustHttp')
except:
    import requests
    print('Huobi 不支持Rust的Request')
    

from urllib import parse
import json
from datetime import datetime
import hmac
import base64
from hashlib import sha256


from main.config import *
from main.hanshu import *

class huobi():

    def __init__(self, apiKey=0, colo=0, ccyBi=0, symbols={}):
        keys = {
            '吃了吗资本': ['1d0eacc8-fc6530a3-rbr45t6yr4-46b93', '996cfc6d-dd71c591-ce9f3def-e267a'],
            '吃了吗资本2': ['afwo04df3f-da801879-b04f2a22-a1e60', '3ed76157-bbec471e-e4b74f99-aaf9d'],
            '火币-球球': ['d384a21e-d57bab25-59e111a6-ghxertfvbf', '046546af-b52c0c5a-28ba50ff-b7aea'],
            '火币-球球2': ['e1db9746-b87d055f-be678798-1qdmpe4rty', 'f733cc52-e7c016b0-1dbbc742-e34a6'],
            '火币-球球3': ['7yngd7gh5g-0f8ce7be-13a2e33f-1a4a7', 'a71a04cb-804cd904-6d886443-ffae2'],
            '火币4': ['d74720fe-8bb90b9e-bvrge3rf7j-24716', '65df10f5-cb217858-8f7bb1ba-c644b'],
            '火币-zhouxi5': ['32d59e0a-rfhfg2mkl3-73aab62e-bf62e', 'feaa3d7c-38f7b00c-7c887c9d-c28e1'], #clmtaoli004
            '火币TEST': ['bgrveg5tmn-1497dffe-af414c18-5a966', 'c48b00fd-105e7b80-03c9d637-c6531'], #AAA002
            'AAA003': ['665cf84a-55c0fee5-dab4c45e6f-907a3', '64d8737b-0874c3d2-c8c84d40-dd147'], #sia
            'AAA004': ['d6e0a299-3d2xc4v5bu-02e7ca1d-b9890', '635e0ebe-515dd5f2-0d6b08ac-66f4a'], #clm
            'AAA005': ['e8a68de9-659da58a-rfhfg2mkl3-7f905', 'e32a0469-7420ea3b-ed63bb7b-9abe5'], #robin
            'AAA006': ['3d089058-d769e073-5b91f178-vfd5ghr532', 'f19f9aa8-fcfec1e6-94b6aa66-0fac6'], #robin
            'AAA007': ['33c4f754-f35cdeb7-bgrveg5tmn-f0e35', 'be4bf244-32673f89-c63d4fb3-155db'], #robin
            'AAA008': ['fr2wer5t6y-7eef8dec-628b58e3-6010e', '5a3b5059-f6396c9f-4894ca7c-19412'], #robin

            'huobi8': ['6ebe62e9-1ee6aa17-ez2xc4vb6n-a685c', 'b5e122bf-3df7a783-265ff29d-ccb5d'],


            '新下级': ['1b00da38-096166da-6a787354-ghjrgrft5g', 'cb9c3c8e-6b5caa48-f6319d26-0d514'],
            '新返佣': ['f488bbba-443d99b4-0ae585cd-yh4fhmvs5k', 'fa8be67f-17c638ab-ea9cf3f1-d23e4'],
            '下级': ['aeb68aa1-283f76fd-1587d72b-1qdmpe4rty', '70bd322f-b1c9d938-86d9254c-6cb8e'],

            'Huobi_球球_1': ['c174bc3e-1qdmpe4rty-4eba4e06-bae7b', '05972de6-ef6c5acd-c2e5673b-5b6b0'],
            'Huobi_Sia_2': ['50ebf0fe-876d6cae-h6n2d4f5gh-d929f', 'badad809-5282a508-60536fc7-1369d'],
            'Huobi_clm_3': ['cdgs9k03f3-34cb4274-3ae36630-83375', '8a933d06-0b8cf23a-6a661d3e-0d019'],
            'Huobi_Robin_4': ['22f75b68-bewr5drtmh-50f8c5b4-e9ec9', '55595b86-6d585203-142db264-7f38d'],
            'Huobi_Robin_5': ['23c07c13-4114d71f-867c1477-ghxertfvbf', '75324f67-a3150383-4db4c151-cb06c'],
            'Huobi_骨刀_6': ['b1rkuf4drg-0ea6b12d-8a0fb8ad-bb0fe', 'bf89ac40-011261bd-9c3ca73d-fa09a'],
            'Huobi_球球_7': ['xa2b53ggfc-32326946-59cd9f2f-6a27a', '13e7b4c8-b9830562-3f788fc3-3cd52'],
            'Huobi_梧桐_8': ['34102dc8-51c4be59-057574a3-ghxertfvbf', 'a179b22c-be42a38c-ea466fdf-4e234'],
            'Huobi_球球_9': ['ghxertfvbf-78dd8e25-0bcb857b-a0073', '66d25cc3-2683db19-211f73fd-fd818'],
            'Huobi_球球_10': ['4d0871b5-09a01b2a-65d7d47f-h6n2d4f5gh', 'b9a55588-a41d2f46-53eaa0cd-30048'],
            'Huobi_clm_11': ['bgrveg5tmn-595210de-14d49d34-5d2ec', 'b1ad17e0-b512f20a-a27f49ac-59d80'],
            'Huobi_pw_12': ['335c01a9-4949b53b-ef50fa5b-ghjrgrft5g', 'd53eb36c-17b19a25-62d3dbd0-79c5f'],
            'Huobi_clm_13': ['cd9133eb-fb0ad95d-ht4tgq1e4t-ca5c7', '4857e595-890783ab-068cecc7-4a338'],

            '梧桐资本': ['52f5d124-f0b0f03a-ghxertfvbf-da080', 'fab95689-7855990f-6d068734-ef7f4'],
            '梧桐1': ['qz5c4v5b6n-77a97fbb-35565281-83adc', 'a0dec344-a3e1fcd6-92fc42d1-a256a'],
            'ltp': ['aca99e3d-dab4c45e6f-c8b96d62-3c630', '547587ed-9f132d91-25746f7e-86a89'],

        }

        self.debug = 0
        self.debugs = []

        apiKey = apiKey if apiKey else Api_Key2
        self.access_id = keys[apiKey][0]
        self.secret_key = keys[apiKey][1]

        # self.HOST = 'https://api.hbdm.com'      #东京A区 均:22.32       东京C区 均:22.65
        self.HOST = 'api.hbdm.vn'       #东京A区 均:13.29       东京C区 均:12.95
        # self.HOST = 'api.hbdm.com'

        self.session = requests.Session()
        
        """ 缓存费率数据"""
        self.feilvData = {}
        self.symbol = symbols

        """ 多出口IP"""
        try:
            ips = DuoIp
        except:
            ips = []

        self.i = 0
        self.ips = []
        self.len_ips = len(ips)
        self.account_id = 0
        
        if len(ips) > 1:
            log('Huobi 使用多IP获取行情', ips)
            for ip in ips:
                from requests_toolbelt.adapters import source  #指定出口IP
                sb = requests.Session()
                new_source = source.SourceAddressAdapter(ip)
                sb.mount('http://', new_source)
                sb.mount('https://', new_source)
                self.ips.append(sb)

        if '均衡' not in ServerName and '提现' not in ServerName:
            self.SetSideModel()
            self.GetSymbolFeilv()

        # print(self.go('POST', '/linear-swap-api/v1/swap_fee'))
        
    
    def dispatch_request(self, method):
        return {
            'GET': self.session.get,
            'DELETE': self.session.delete,
            'PUT': self.session.put,
            'POST': self.session.post,
        }.get(method, 'GET')


    def get_url_suffix(self, method:str, access_key:str, secret_key:str, host:str, path:str)->str:
        timestamp = datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%S')
        timestamp = parse.quote(timestamp)
        suffix = 'AccessKeyId={}&SignatureMethod=HmacSHA256&SignatureVersion=2&Timestamp={}'.format(access_key, timestamp)
        payload = '{}\n{}\n{}\n{}'.format(method.upper(), host, path, suffix)

        digest = hmac.new(secret_key.encode('utf8'), payload.encode('utf8'), digestmod=sha256).digest()
        signature = base64.b64encode(digest).decode()

        suffix = '{}&Signature={}'.format(suffix, parse.quote(signature))
        return suffix

    # 请求API
    def go(self, method, path, payload=None, needKey=1, spot=0, ref=1):


        if self.ips:
            self.session = self.ips[self.i]
            self.i += 1
            if self.i >= self.len_ips:
                self.i = 0

        host = 'api-aws.huobi.pro' if spot else self.HOST
        if needKey:
            url = 'https://{}{}?{}'.format(host, path, self.get_url_suffix(method, self.access_id, self.secret_key, host, path))

        else:
            url = 'https://' + host + path


        headers = {
            "Accept": "application/json",
            "Content-type": "application/json"
        }

        if method == 'POST':
            params = {'url': url, 'timeout': 5, 'json': payload, 'headers': headers}
        else:
            params = {'url': url, 'timeout': 5, 'params': payload, 'headers': headers}


        if self.debug:
            t = NowTime_ms()

        # print(self.session.headers)
        try:
            response = self.dispatch_request(method)(**params)
            if response.text:
                response2 = response.json()
            else:
                response2 = ''

        except Exception as e:
            traceback.print_exc()

            print('请求API失败', url)
            try:
                print(response.text)
            except:
                pass
            time.sleep(1)
            if not ref:
                return False
            return self.go(method, path, payload, needKey, ref=0)

        if self.debug:
            yc = NowTime_ms() - t
            self.debugs.append(yc)
            print(path, str(yc)+'ms', 'min:'+str(min(self.debugs)), 'max:'+str(max(self.debugs)), '均:'+str(round(sum(self.debugs)/len(self.debugs),2)))

        return response2

    """撤销所有挂单"""
    def DeleteAllOrder(self, symbol):
        t = NowTime_ms()
        data = self.go("POST", "/linear-swap-api/v1/swap_cross_cancelall", {'contract_code': symbol})
        log('Huobi 撤单', symbol, data, str(NowTime_ms()-t)+'ms')
        return data

    """ 获取所有交易对资金费率"""
    def GetSymbolFeilv(self, symbol=0):
        if not self.feilvData or not symbol:
            data = self.go("GET", '/linear-swap-api/v1/swap_batch_funding_rate', {}, needKey=0)
            if not data or 'data' not in data:
                print('获取资金费率失败', data)
                time.sleep(0.1)
                return self.GetSymbolFeilv(symbol)

            data = data['data']
            if not symbol:
                return data

            for v in data:
                if v['funding_time']:
                    v['funding_time'] = int(v['funding_time'])
                    v['next_funding_time'] = int(v['next_funding_time'])
                    self.feilvData[v['contract_code']] = v

        try:
            symbolData = self.feilvData[symbol]
        except:
            symbolData = 0

        if not symbolData or NowTime() >= symbolData['next_funding_time']+2:
            log(symbol, '更新资金费率',
                 '更新时间', GetTime(symbolData['next_funding_time'], '%m-%d %H:%M:%S') if symbolData else '',
                 '获取时间', GetTime(symbolData['funding_time'], '%m-%d %H:%M:%S') if symbolData else ''
                 )

            self.feilvData = {}
            return self.GetSymbolFeilv(symbol)

        else:
            # print(NowTime_ms(), symbolData['nextFundingTime'])
            # print(symbol, '获取资金费率',
            #      '更新时间', GetTime(symbolData['nextFundingTime'], '%m-%d %H:%M:%S') if symbolData else '',
            #      '获取时间', GetTime(symbolData['time'], '%m-%d %H:%M:%S') if symbolData else ''
            #      )
            return round(float(symbolData['funding_rate'])*100, 4)


    """ 获取最佳挂单"""
    def GetTickerAll(self):
        data = self.go("GET","/linear-swap-ex/market/bbo", {}, needKey=0)

        if not data or 'ticks' not in data:
            print('Huobi 获取所有盘口失败', data)
            time.sleep(0.1)
            return self.GetTickerAll()

        oks = {}
        for v in data['ticks']:
            oks[v['contract_code']] = v

        return oks
        

    """ 获取交易对规则"""
    def GetSymbols(self):
        return self.go("GET", '/linear-swap-api/v1/swap_contract_info', {}, needKey=0)


    """ 获取交易对规则"""
    def GetSymbolsGg(self):
        return self.go("POST", '/linear-swap-api/v1/swap_cross_available_level_rate', {})['data']


    """ 获取交易对规则
    "limit":限价，"opponent":对手价，"lightning":闪电平仓，"optimal_5":最优5档，"optimal_10":最优10档，"optimal_20":最优20档，"fok":FOK订单，"ioc":IOC订单,opponent_ioc"： 对手价-IOC下单，"lightning_ioc"：闪电平仓-IOC下单，"optimal_5_ioc"：最优5档-IOC下单，"optimal_10_ioc"：最优10档-IOC下单，"optimal_20_ioc"：最优20档-IOC下单,"opponent_fok"： 对手价-FOK下单，"lightning_fok"：闪电平仓-FOK下单，"optimal_5_fok"：最优5档-FOK下单，"optimal_10_fok"：最优10档-FOK下单，"optimal_20_fok"：最优20档-FOK下单"""
    def GetSymbolsLimit(self, order_price_type='ioc'):
        return self.go("POST", '/linear-swap-api/v1/swap_order_limit', {'order_price_type': order_price_type, 'contract_type': 'swap'})['data']['list']
        

    """获取可用余额"""
    def GetYuer(self, p=1):

        if self.access_id == '1b00da38-096166da-6a787354-ghjrgrft5g':
            data = self.go("GET","/linear-swap-api/v3/unified_account_info")
        else:
            data = self.go("POST","/linear-swap-api/v1/swap_cross_account_info")
    
        if not data or 'data' not in data:
            print('Huobi 获取余额失败', data)
            time.sleep(1)
            return self.GetYuer(p)
            
        no = 0
        ok = 0
        for v in data['data']:
            if ('margin_asset' in v and v['margin_asset'] == CcyBi) or\
                 ('margin_account' in v and v['margin_account'] == CcyBi): #USDT
                ok = v
                # pprint(v)
                break
        if not ok:
            no = 1

        if no:
            print('Huobi 获取余额失败', data)
            time.sleep(1)
            return self.GetYuer(p)

        ok['all'] = Si(ok['margin_balance'], 2)
        ok['keyong'] = N(ok['withdraw_available'], 4)
        ok['yingkui'] = N(ok['cross_profit_unreal'] if 'cross_profit_unreal' in ok else ok['profit_unreal'], 4)
        
        ok['all'] = ok['all'] if ok['all'] else 0.01
        ok['keyong'] = ok['keyong'] if ok['keyong'] else 0.01

        if p:
            log(p+'  Huobi USDT总余额' if type(p) == str else 'Huobi USDT总余额', ok['all'], '可用', N(ok['keyong'], 4),
                 '持仓盈亏', N(ok['yingkui'], 4))

        return ok


    
    """ 获取所有持仓"""
    def GetPos(self):
        data = self.go('POST', "/linear-swap-api/v1/swap_cross_position_info", {
            'contract_type': 'swap', #调整成全仓
        })
        
        try:
            if 'data' in data and 'status' in data and data['status'] == 'ok':
                okdata = data['data']
                data = []
                for v in okdata:
                    ok = {}
                    ok['symbol'] = v['contract_code']
                    ok['liang'] = abs(v['volume'])
                    ok['side'] = v['direction'].upper() #持仓方向
                    ok['side2'] = 'SELL' if ok['side'] == 'BUY' else 'BUY' #平仓方向

                    ok['jiage'] = float(v['cost_open'])
                    ok['nowJiage'] = float(v['last_price'])    #最新成交价格
                    ok['yingkui'] = N(float(v['profit_unreal']), 4)
                    ok['bzj'] = N(float(v['position_margin']), 4)
                    ok['roe'] = float(v['profit_rate'])

                    data.append(ok)

                return data

            else:
                log(Color("火币 获取持仓失败", -1), data)
                time.sleep(3)
                return self.GetPos()

        except Exception as e:
            uploadError(traceback.format_exc())

        log(Color("获取持仓失败", -1), data)
        uploadLog(isExit=1)
        os._exit(0)


    """设置持仓模式"""
    def SetSideModel(self):
        post = {
            'margin_account': CcyBi, #调整成全仓
            'position_mode': 'single_side',
        }
        fh = self.go('POST', "/linear-swap-api/v1/swap_cross_switch_position_mode", post)
        log('火币 设置单向持仓', 'ok' if 'data' in fh else fh)


    """设置杠杆倍数和持仓方向，火币不需要这个，下单的时候填写，并且需要有持仓的时候才可以切换"""
    def SetGangGan(self, symbol, beishu):
        if not beishu:
            return
        post = {
            "contract_code": symbol.lower(),
            "lever_rate": beishu,
        }

        fh = self.go('POST', "/linear-swap-api/v1/swap_cross_switch_lever_rate", post)
        log('火币设置杠杠', post, fh)
        time.sleep(0.5)



    """下单"""
    def PostOrder(self, symbol, side, jiage, liang, type='ioc', jiancang=0, msg2=''):
        jiage = str(jiage)
        liang = int(liang)

        msg = '火币　'+symbol+'　'

        if jiancang:
            msg += '平空仓' if side == 'BUY' else '平多仓'

        else:
            msg += '开多仓' if side == 'BUY' else '开空仓'

        msg += "　方向:"+side+"　价格:"+jiage+"　量:"+str(liang)+"　减仓:"+str(jiancang)+"　Type:"+type

        post = {
            'contract_code': symbol.lower(),
            'price': jiage,
            'lever_rate': self.symbol[symbol]['GangGan'] if symbol in self.symbol else SetGangGan,
            'volume': liang,
            'direction': side.lower(),
            'order_price_type': type,
        }

        if type == 'normal':
            post['order_price_type'] = 'market'

        if jiancang:
            post['reduce_only'] = jiancang

        orderId = 0
        
        t = NowTime_ms()

        for x in range(2):
            order = self.go('POST', "/linear-swap-api/v1/swap_cross_order", post)

            # log("下单返回", order)
            if 'data' not in order or 'order_id' not in order['data']:
                log(symbol+" [!!!] 套利下单失败！！重试", post, order)
                if 'err_code' in order and order['err_code'] == 1349:
                    post['lever_rate'] = 10
                    uploadError(symbol+' 火币平仓：'+str(post)+'  '+str(order))
                    continue
                # if 'err_code' in order and order['err_code'] == 1056:
                #     time.sleep(10)
                #     break

                # if 'err_code' not in order or order['err_code'] not in [1492]: #1349 为 切换了杠杆
                # uploadError(symbol+' 火币开仓：'+str(post)+'  '+str(order))
                
                # time.sleep(1)
                break
            else:
                orderId = order['data']['order_id']
                break
        
        msg = [msg+ '　'+ Color(msg2, 1), str(NowTime_ms()-t)+'ms', orderId]
        
        return orderId, msg


    """ 撤销订单"""
    def DelOrder(self, symbol, order_id):
        post = {
            'order_id': order_id,
            'contract_code': symbol,
        }

        t = NowTime_ms()

        for x in range(50):
            order = self.go('POST', "/linear-swap-api/v1/swap_cross_cancel", post)

            # log("下单返回", order)
            if 'status' not in order or order['status'] != 'ok':
                log(symbol+" [!!!] 套利撤单失败！！重试", post, order)
                orderId = order
                time.sleep(1)

            else:
                orderId = order['status']
                break
        
        msg = [symbol, order_id, '撤单', str(NowTime_ms()-t)+'ms', orderId]
        print(*msg)
        
        return orderId, msg



    
    """市价平仓"""
    def ClosePos(self, symbol, jiage=0, liang=0, side='', MZ=1):
        t = NowTime_ms()
        liang = int(liang)
        data = {
            'contract_code': symbol.lower(),
            'volume': liang,
            'direction': side.lower(),
        }

        for i in range(2):
            fh2 = self.go("POST","/linear-swap-api/v1/swap_cross_lightning_close_position", data)
            if 'data' in fh2 :
                fh = fh2['data']['order_id']
                break
            else:
                log('火币套利市价平仓Error', data, fh2, Color("", -1))
                # uploadError(symbol+' 火币平仓：'+str(data)+'  '+str(fh2))

                data['volume'] /= 2
                data['volume'] = int(data['volume'])
                fh = 0

        msg = ["火币", Color("TakerClose 套利单腿成交", -1), side, symbol,
         "价格", jiage, "量", str(liang)+' ('+STR_N(liang*jiage*MZ)+'$)', str(NowTime_ms()-t)+'ms']

        return fh, msg

    """ 获取USERID"""
    def GetUserId(self):
        fh = self.go('GET', '/v2/user/uid', {}, spot=1)
        return fh['data']

    """ 最大持仓限制"""
    def GetMaxPos(self):
        data = self.go("POST", "/linear-swap-api/v1/swap_cross_position_limit")
        return data['data']

    """ 子母账户相互转账"""
    def ziHua(self, liang, uid, side, bi=''):
        bi = bi if bi else CcyBi
        data = {
            'sub-uid': uid,
            'currency': bi.lower(), 
            'amount': str(liang),
            'type': 'master-transfer-out' if side == 'in' else 'master-transfer-in',
        }

        # pprint(data)
        data = self.go("POST", "/v1/subuser/transfer", data, spot=1)
        log('[Huobi子母]', '主账户转入' if side == 'in' else '子账户转出', '币种', bi, liang, '结果', data, Color('', 1))

        return 'status' in data and data['status'] == 'ok'
    

    """ 现货和合约之间的划转"""
    def huazhuan(self, liang, side, bi=''):
        bi = bi if bi else CcyBi
        data = {
            'margin-account': bi,
            'currency': bi,
            'amount': str(liang),
        }
        if side=='in':
            data['from'] = 'spot'
            data['to'] = 'linear-swap'
        else:
            data['from'] = 'linear-swap'
            data['to'] = 'spot'


        # pprint(data)
        data = self.go("POST", "/v2/account/transfer", data, spot=1)

        log('[Huobi划转]', '现货转入合约' if side == 'in' else '合约转入现货', '币种', bi, liang, '结果', data, Color('', 1))
        return 'message' in data and data['message'] == 'Succeed'
    

    """提现"""
    def Tixian(self, dizhi, liang, bi='', net='trc20usdt'):
        bi = bi if bi else CcyBi
        data = self.go("POST", "/v1/dw/withdraw/api/create",
         {'currency':bi.lower(), 'address':dizhi, 'chain': net, 'amount':liang},
          spot=1)
        log('Huobi 提现 地址', dizhi, '数量',liang, '币种', bi, '结果', data)
        return data


    """ set account id"""
    def SetAccountId(self):
        data = self.go('GET', '/v1/account/accounts', {}, spot=1)
        self.account_id = data['data'][0]['id']
        # log('火币获取账户id', self.account_id)

    """获取现货余额"""
    def GetXianhuo(self, bi=''):
        bi = bi if bi else CcyBi

        if not self.account_id:
            self.SetAccountId()

        data = self.go('GET', f'/v1/account/accounts/{self.account_id}/balance', {}, spot=1)
        for v in data['data']['list']:
            if v['type'] != 'trade':
                continue

            if v['currency'].upper() == bi:
                return N(v['balance'], 4)
        
        else:
            log('火币获取现货余额失败', data)
            time.sleep(10)
            return self.GetXianhuo(bi)