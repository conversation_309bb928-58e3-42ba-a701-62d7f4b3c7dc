#!/usr/bin/python
# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from urllib import request
import urllib3
import urllib
from urllib3.exceptions import InsecureRequestWarning

urllib3.disable_warnings(InsecureRequestWarning)
http = urllib3.PoolManager(timeout=urllib3.Timeout(connect=1, read=2))
from uuid import uuid4
import time
import ujson
import traceback  # 输出报错
from pprint import pprint

import requests
from urllib import parse
import json
from datetime import datetime
import hmac
import base64
from hashlib import sha256
from hashlib import md5
import random

from main.config import *
from main.hanshu import *


def parse_params_to_str(params):
    url = '?'
    for key, value in params.items():
        url = url + str(key) + '=' + str(value) + '&'
    return url[0:-1]


NC = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678"
def gen_randomstr(n=6):
    rmstr = ""
    for _ in range(n):
        rmstr += NC[random.randint(0, 47)]
    return rmstr


class deepcoin():

    def __init__(self, apiKey=0, colo=0, ccyBi=0, symbols={}):
        keys = {
            '制度': 'sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%229159337%22%2C%22first_id%22%3A%22189ed6056f19cb-0cdb5698f7c86-3e604809-2073600-189ed6056f2bb7%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTg5ZWQ2MDU2ZjE5Y2ItMGNkYjU2OThmN2M4Ni0zZTYwNDgwOS0yMDczNjAwLTE4OWVkNjA1NmYyYmI3IiwiJGlkZW50aXR5X2xvZ2luX2lkIjoiOTE1OTMzNyIsImlkZW50aXR5X2g1X2lkIjoicGMtNWVkYTFmNDdiOGFjNWViOWE2NTQ2ZjE2NDljZGY2YmQifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%229159337%22%7D%2C%22%24device_id%22%3A%22189ed6056f19cb-0cdb5698f7c86-3e604809-2073600-189ed6056f2bb7%22%7D	vlosT5FqtuHPowsC5IyKooWNaN4kghaEL2qJeIt431bdF3u1L4fSb/eAKl8x4yDO/NnxmJ+aJwcrxblI+BgF6w==	pc-5eda1f47b8ac5eb9a6546f1649cdf6bd	Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36',
        }

        
        apiKey = apiKey if apiKey else Api_Key2
        if apiKey in keys:
            apiKey = keys[apiKey].split('	') if type(keys[apiKey]) == str else keys[apiKey]
        else:
            apiKey = GetApiKey(apiKey, 'Deepcoin')[0].split('	')
        
        self.auth_headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "Device": apiKey[2],
            "Token": apiKey[1],
            "Otoken": apiKey[1],
            "Cookie": apiKey[0].replace("\n", "").replace("\r", ""),
            "Platform": "pc",
            "Lang": "en",
            "Appid": "547798",
            "version": "v3.9.28",
            "User-Agent": apiKey[3],
        }
        self.token = apiKey[1]

        self.debug = 0
        self.debugs = []
        self.ccyBi = ccyBi if ccyBi else CcyBi
        self.HOST = 'https://api.deepcoin.com'

        self.session = requests.Session()

        """ 缓存费率数据"""
        self.feilvData = {}
        self.symbol = {}

        """ 多出口IP"""
        try:
            ips = DuoIp
            if len(ips) > 1:
                log('deepcoin 使用多IP交易', ips)
        except:
            ips = []

        self.i = 0
        self.ips = []
        self.len_ips = len(ips)
        self.preRestTime = 0

        # 网站固定的
        self.appid = 547798
        self.appsecret = "61dd6c49529a05569900e71f49a0cd87"

        for ip in ips:
            from requests_toolbelt.adapters import source  # 指定出口IP
            sb = requests.Session()
            new_source = source.SourceAddressAdapter(ip)
            sb.mount('http://', new_source)
            sb.mount('https://', new_source)
            self.ips.append(sb)
        
        self.account_id = self.GetAccountId()

        if '操盘_' in ServerName:
            spot = self.GetYuer(name='wallet')
            if spot['keyong'] > 1:
                self.huazhuan(spot['keyong'])
            spot = self.GetYuer(name='fanyong')
            if spot['keyong'] > 1:
                self.huazhuan(spot['keyong'], 'fanyong')
            spot = self.GetYuer(name='spot')
            if spot['keyong'] > 1:
                self.huazhuan(spot['keyong'], 'spot')



    def dispatch_request(self, method):
        return {
            'GET': self.session.get,
            'DELETE': self.session.delete,
            'PUT': self.session.put,
            'POST': self.session.post,
        }.get(method, 'GET')

    def get_sign(self, payload):
        sortedObj = {}
        s = ""
        for i in sorted (payload) : 
            sortedObj[i] = payload[i]
        s = urlencode(sortedObj)
        sign = hmac.new(self.appsecret.encode('utf-8'), s.encode('utf-8'), sha256).hexdigest()
        sign = md5(sign.encode('utf-8')).hexdigest()
        return sign

    # 请求API
    def go(self, method, path, payload=None, needKey=1):

        url = self.HOST + path

        if self.ips:
            self.session = self.ips[self.i]
            self.i += 1
            if self.i >= self.len_ips:
                self.i = 0

        params = ''

        headers = {
            'Accept': 'application/json',
            'Content-type': 'application/json'
        }

        # 更新url 和 header
        if needKey:
            headers = self.auth_headers

            url = path if needKey == 2 else 'https://net-wapi.deepcoin.com' + path         

        if method == 'POST':
            params = {'url': url, 'timeout': 5, 'data': ujson.dumps(payload), 'headers': headers}
        else:
            params = {'url': url, 'timeout': 5, 'params': payload, 'headers': headers}

        if self.debug:
            t = NowTime_ms()

        response = ''
        # print(self.session.headers)
        try:
            response = self.dispatch_request(method)(**params)
            if response.text:
                response2 = response.json()
            else:
                response2 = ''

        except Exception as e:
            traceback.print_exc()

            print('请求API失败', url)
            if response:
                if '<script' not in response.text:
                    print(response.text)
            time.sleep(1)
            return self.go(method, path, payload, needKey)

        if self.debug:
            yc = NowTime_ms() - t
            self.debugs.append(yc)
            print(path, str(yc) + 'ms', 'min:' + str(min(self.debugs)), 'max:' + str(max(self.debugs)),
                  '均:' + str(round(sum(self.debugs) / len(self.debugs), 2)))
        return response2


    """ 获取交易对规则"""
    def GetSymbols(self, name='SPOT'):
        res = self.go("GET", '/deepcoin/market/instruments', {"instType": name}, needKey=0)['data']  # 修改 API 路径
        return res


    """ 获取交易对规则"""
    def GetSymbolsList(self):
        rdmstr = gen_randomstr()
        params = {
            "appid": self.appid,
            "convertPOST": 1,
            "flag": "1",
            "randomstr": rdmstr,
            "showall": "1",
            "system": "SwapU",
            "timestamp": int(time.time() * 1000)
        }
        params['sign'] = self.get_sign(params)

        res = self.go("POST", '/common/symbol-list', params, needKey=1)['retData']['data']  # 修改 API 路径
        return res


    """ 获取合约币种持仓"""
    def GetPos(self, symbol=0, all=0):
        params = [{"Action":"position","Method":"GET","Param":{"ProductGroup":"SwapU","pageIndex":1,"pageSize":500,"MemberID":"9154578"},"ExchangeID":"DeepCoin"}]

        res = self.go("POST", "https://www.deepcoin.com/query/v1.0/SendBatch", params, 2)

        if type(res) != list:
            print('deepcoin 获取持仓失败', res)
            time.sleep(1)
            return self.GetPos()

        data = []
        try:
            okk = res[0]['Result']['data']
            for v in okk:
                if 'CostPrice' in v and float(v['Position']):
                    ok = {}
                    ok['symbol'] = v['InstrumentID']
                    ok['liang'] = abs(float(v['Position']))

                    side = 'BUY'
                    if v['PosiDirection'] == '1' or float(v['Position']) < 0:
                        side = 'SELL'

                    ok['side'] = side
                    ok['side2'] = fanSide(side)
                    ok['jiage'] = float(v['CostPrice'])
                    ok['nowJiage'] = 0
                    ok['yingkui'] = 0#float(v['PROFITPERCENT']) * float(v['TotalPositionCost'])
                    ok['bzj'] = 0
                    ok['roe'] = 0
                    ok['time'] = NowTime_ms()
                    data.append(ok)

            return data

        except Exception as e:
            uploadError(traceback.format_exc())
        
        log(Color("获取合约持仓失败", -1), data)
        uploadLog(isExit=1)
        os._exit(0)


    """下单"""
    def PostOrder(self, symbol, side, jiage, liang, type='ioc', jiancang=0, msg2=''):
        liang2 = str(liang) + ' (' + STR_N(float(liang) * float(jiage)) + '$)'

        rdmstr = gen_randomstr()


        post = {
            "ExchangeID": "DeepCoin",
            "MemberID": str(self.account_id),
            "InstrumentID": symbol,
            "UserID": str(self.account_id),
            "Price": jiage if jiage else 0,
            "AccountID": str(self.account_id),
            "Volume": int(liang),
            "IsCrossMargin": 1,
            "TradeUnitID": str(self.account_id),
            "BusinessType": "V",
            "appid": 547798,
            "randomstr": rdmstr,
            "convertPOST": 1,
            "timestamp": int(time.time() * 1000)
        }
        if not post['Volume']:
            return log('下单量为0', liang)

        post['Direction'] = "0" if side == 'BUY' else "1"

        if type == 'ioc':
            post['OrderPriceType'] = '0'
            post['OrderType'] = '1'
        if type == 'limit':
            post['OrderPriceType'] = '0'
        else:
            post['OrderPriceType'] = '4' 
            del post["Price"]


        msg = 'deepcoin ' + symbol + ' '

        if jiancang:
            post['OffsetFlag'] = '1'
            msg += '平空仓' if side == 'BUY' else '平多仓'
        else:
            post['OffsetFlag'] = '0'
            msg += '开多仓' if side == 'BUY' else '开空仓'

        msg += "　方向:" + side + "　价格:" + str(jiage) + "　量:" + liang2 + "　减仓:" + str(jiancang) + "　Type:" + type

        post['sign'] = self.get_sign(post)
        orderId = 0
        # print(post)
        t = NowTime_ms()
        for x in range(1):
            self.auth_headers['referer'] = 'https://www.deepcoin.com/zh/SwapU?currentId='+symbol
            self.auth_headers['requestid'] = md5(gen_randomstr(32).encode('UTF-8')).hexdigest()
            res = self.go('POST', "https://www.deepcoin.com/v2/public/cpt/SendOrderInsert", payload=post, needKey=2)
            del self.auth_headers['requestid']

            # pprint(res)
            if not res or 'msg' not in res or res['msg'] != 'OK' or res['data']['errorCode'] != 0:
                log(symbol + " [!!!] 下单失败！！重试", post, res)
                uploadError('下单失败:'+str(res))

            else:
                orderId = res['data']['result'][0]['data']['OrderSysID']
                break

        msg = [msg + '　' + Color(msg2, 1), str(NowTime_ms() - t) + 'ms', orderId]

        return orderId, msg


    def GetAccountId(self):
        rdmstr = gen_randomstr()
        params = {
            "appid": self.appid,
            "convertPOST": 1,
            "product": "Swap",
            "randomstr": rdmstr,
            "timestamp": int(time.time() * 1000)
        }
        params['sign'] = self.get_sign(params)
        result = self.go("POST", "/user-account/account-list", params)

        if result['retCode'] != 0:
            uploadError('deepcoin 获取Account Id失败:'+str(result))
            time.sleep(5*60)
            os._exit(0)
        
        return result['retData']['account_list'][0]['account_id']
        
    """获取可用余额"""
    def GetYuer(self, p=1, ccyBi=0, name='swapU'):
        if name == 'fanyong':
            name = 'rebate'

        rdmstr = gen_randomstr()
        params = {
            "account_id": self.account_id,
            "appid": self.appid,
            "randomstr": rdmstr,
            "timestamp": int(time.time() * 1000),
            "convertPOST": 1,
        }
        params['sign'] = self.get_sign(params)

        res = self.go("GET", f"/v1/balance/account-balance?{urlencode(params)}")
        if res['retCode'] != 0:
            print('deepcoin 获取余额失败', res)
            time.sleep(1)
            return self.GetYuer(p, ccyBi, name)

        okk = res['retData']['product_list'][name]
        ok = {}
        ok['all'] = Si(okk['total']['amount_usdt'], 4)

        for i in okk['list']:
            if i['symbol'] == 'USDT':
                ok['keyong'] = Si(i['available'], 4)
                break
                
        ok['yingkui'] = 0

        ok['all'] = ok['all'] if ok['all'] else 0.01
        ok['keyong'] = ok['keyong'] if ok['keyong'] else 0.01

        if p:
            log(name, 'deepcoin USDT总余额', ok['all'], '可用', N(ok['keyong'], 4),
                '持仓盈亏', N(ok['yingkui'], 4))

        return ok

    """U划转"""
    def huazhuan(self, liang, name='wallet', bi=''):
        bi = bi if bi else CcyBi
        params = {
            "from_account_id":self.account_id,
            "to_account_id":self.account_id,
            "amount": str(liang),
            "from_id": 2,
            "to_id": 7,
            "currency_id": bi,
            "appid": self.appid,
            "randomstr": gen_randomstr(),
            "timestamp": int(time.time() * 1000),
            "convertPOST":1,
        }
        msg = '钱包 划转到 U本位'
        if name == 'fanyong':
            msg = '返佣 划转到U 本位'
            params['from_id'] = 3
        if name == 'spot':
            msg = '现货 划转到U 本位'
            params['from_id'] = 1

        params['sign'] = self.get_sign(params)

        res = self.go("POST", f"https://net-wapi.deepcoin.com/transfer/transfer", params, needKey=2)
        log(msg, bi, liang, '返回', res)
        return res


    def GetMaxGangGan(self, symbol, maxPos=0):
        rdmstr = gen_randomstr()
        params = {
            "ExchangeID":"DeepCoin",
            "ProductGroup":"SwapU",
            "AccountID": self.account_id,
            "InstrumentID": symbol,
            "MemberID": self.account_id,
            "PosiDirection":"0",
            "TradeUnitID": self.account_id,
            "appid":547798,
            "randomstr": rdmstr,
            "timestamp": int(time.time() * 1000),
            "convertPOST":1,
        }
        params['sign'] = self.get_sign(params)

        fh = self.go('GET', "https://www.deepcoin.com/action/v1.0/SendQryLeverage", params, needKey=2)
        fh = fh['result'][0]['data']
        
        log(f'deepcoin {symbol} 最大杠杠', fh['DefaultMaxLeverage'], '最大可开', fh['LongMaxVolume'])

        if not maxPos:
            return int(fh['DefaultMaxLeverage'])
        
        else:
            return float(fh['LongMaxVolume'])


    """ 撤销所有挂单"""
    def DeleteAllOrder(self, symbol=0):

        t = NowTime_ms()

        rdmstr = gen_randomstr()
        params = {
            "ActionFlag": "1",
            "ExchangeID": "DeepCoin",
            "MemberID": self.account_id,
            "ProductGroup": "SwapU",
            "UserID": str(self.account_id),
            "appid": 547798,
            "convertPOST": 1,
            "randomstr": rdmstr,
            "timestamp": int(time.time() * 1000),
        }
        params['sign'] = self.get_sign(params)

        fh = self.go('POST', "https://www.deepcoin.com/prism/action/swap/rvk", params, needKey=2)['msg']
        
        log(f'deepcoin {symbol} 撤销全部挂单', fh, NowTime_ms()-t, 'ms')
        return fh

    """ 设置持仓模式：Flase 单向持仓"""
    def SetChicang(self, model=False):
        return '有超强验证'
        t = NowTime_ms()
        rdmstr = gen_randomstr()
        params = {
            "UserID": str(self.account_id),
            "positionType": "2" if not model else "1",
            "token": self.token,
            "appid": 547798,
            "randomstr": rdmstr,
            "timestamp": int(time.time() * 1000),
            "convertPOST": 1,
        }
        params['sign'] = self.get_sign(params)

        pprint(params)
        fh = self.go('POST', "https://www.deepcoin.com/prism/copytrade/position-type", params, needKey=2)
        pprint(fh)
        input('')

        log(f'Deepcoin 持仓模式',  "单向持仓" if not model else "双向持仓", fh, NowTime_ms()-t, 'ms')
        return fh



    def SetGangGan(self, symbol, beishu):

        rdmstr = gen_randomstr()
        params = {
            'ExchangeID': 'DeepCoin',
            'InstrumentID': symbol,
            'MemberID': self.account_id,
            'ActionType': '1',
            'AccountID': self.account_id,
            'TradeUnitID': self.account_id,
            'IsCrossMargin': 1,
            'LongLeverage': int(beishu),
            'ShortLeverage': int(beishu),
            'UserID': self.account_id,
            'appid': 547798,
            'randomstr':rdmstr,
            'timestamp': int(time.time() * 1000),
            'convertPOST': 1,
        }
        params['sign'] = self.get_sign(params)
        fh = self.go('GET', "https://www.deepcoin.com/action/v1.0/SendPositionLeverage", params, needKey=2)
        maxPos = fh['result'][-1]['data']['LongMaxVolume']
        maxLever = fh['result'][-1]['data']['LongMaxLeverage']
        log('Deepcoin 设置杠杆', symbol, beishu, fh['errorMsg'], '最大可开', maxPos, '最大杠杆', maxLever)
        
        return fh['result'][-1]['data']['LongMaxVolume']



if __name__ == "__main__":
    k = deepcoin()
    balance = k.GetYuer()
    #print("当前获取现货余额：%s" % balance)
    symbols = k.GetSymbols()
    print("市场现货列表：%s" % symbols[:5])
    #hold = k.GetPos()
    #print("合约持仓：%s" % hold)
    order = k.PostOrder('BTCUSDT', 'BUY', 24000, 5, type='market')
    print("下单：%s" % str(order))
    #k.GetListenKey()
    #print(k.GetMaxGangGan("DOGEUSDT"))


