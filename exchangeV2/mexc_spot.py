#!/usr/bin/python
# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from urllib import request
import urllib3
import urllib
from urllib.parse import urlencode
from urllib3.exceptions import InsecureRequestWarning

urllib3.disable_warnings(InsecureRequestWarning)
http = urllib3.PoolManager(timeout=urllib3.Timeout(connect=1, read=2))

import time
import ujson
import traceback        #输出报错
from pprint import pprint

import requests
from urllib import parse
import json
from datetime import datetime
import hmac
import base64
import hashlib
from uuid import uuid4
from decimal import Decimal

from main.config import *
from main.hanshu import *

def parse_params_to_str(params):
    url = '?'
    for key, value in params.items():
        url = url + str(key) + '=' + str(value) + '&'
    return url[0:-1]

    
class mexc_spot():

    def __init__(self, apiKey=0, ccyBi=0, symbols={}):
        keys = {
            '吃了吗资本': ['mx0vglMlilJP0MK0ez', '405f73e6f68e4c229e4559d8e329fd0c', '_ga=GA1.1.886919328.1668616385; _rdt_uuid=1668616386746.8b53b012-ce4e-48be-8bab-36412780e4a9; _fbp=fb.1.1668616387166.938911317; _ym_d=1668616387; _ym_uid=1668616387252644548; G_ENABLED_IDPS=google; __zlcmid=1D0lNx27LCnP5KV; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2229321fc7b6d04583b034ca99ead9451d%22%2C%22first_id%22%3A%2218481494356532-09d4f271eb120d-3e604809-2073600-18481494357a78%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%2C%22%24latest_utm_source%22%3A%22mexc%22%2C%22%24latest_utm_medium%22%3A%22sitepop%22%2C%22%24latest_utm_campaign%22%3A%22trading-activity202303%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTg0ODE0OTQzNTY1MzItMDlkNGYyNzFlYjEyMGQtM2U2MDQ4MDktMjA3MzYwMC0xODQ4MTQ5NDM1N2E3OCIsIiRpZGVudGl0eV9sb2dpbl9pZCI6IjI5MzIxZmM3YjZkMDQ1ODNiMDM0Y2E5OWVhZDk0NTFkIn0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%2229321fc7b6d04583b034ca99ead9451d%22%7D%2C%22%24device_id%22%3A%2218481494356532-09d4f271eb120d-3e604809-2073600-18481494357a78%22%7D; mxc_theme_main=light; mxc_reset_tradingview_key=false; g_state={"i_p":1694514728038,"i_l":4}; NEXT_LOCALE=zh-CN; mexc_fingerprint_visitorId=qGOcWJ9M1421tWlql1QC; _vid_t=CoDHhSD6oFAYT9Oy6fIYlpRGyvQfG7ZxJ91W7nKH8478d1e98hGzrem7rLKfZ+KZbDe9e4qJoSoowqifxJK08+fN/v+Ewg7VY+mTHSk=; uc_token=WEB90f66c0b58bcc21de4392c0d18890c9e3550be65994a63a058911b6ed4d03cb2; x-mxc-fingerprint=qGOcWJ9M1421tWlql1QC; u_id=WEB90f66c0b58bcc21de4392c0d18890c9e3550be65994a63a058911b6ed4d03cb2; _ga_L6XJCQTK75=GS1.1.1698419821.85.1.1698419911.35.0.0; RT="z=1&dm=www.mexc.com&si=c08ce342-0700-400a-978b-9958330f8627&ss=lo8rauol&sl=8&tt=g0a&bcn=%2F%2F684d0d47.akstat.io%2F&ld=29m6"',
             'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36'],
            '小号1': ['mx0vglGv0SFtpvYwrQ', '9626cfff4c104ffc942719f1fa4cb6d6', 'G_ENABLED_IDPS=google; _ga=GA1.1.988736951.1678602240; __zlcmid=1EqlfGwrwZewRzx; _rdt_uuid=1688293930211.3575f9c4-0d25-4594-b173-ec8cd7fd12bb; _fbp=fb.1.1688293930867.1656919797; mexc_fingerprint_visitorId=HkvEdYtighNyJm9GQjJC; _vid_t=jAqh+xd2XZ4k155kSEV/cyy43htdutB1r/3cGMjiPYmGCfpityCnvbOiOHVW7O8duXBdloVBQk2b9KBWbWuBdW7uKw==; uc_token=WEB2f7238c32d447d11ebed72a209b43c6972c66d6c9d24f1a1a4fb648e4f5cf231; x-mxc-fingerprint=HkvEdYtighNyJm9GQjJC; u_id=WEB2f7238c32d447d11ebed72a209b43c6972c66d6c9d24f1a1a4fb648e4f5cf231; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22892307a23d4d402d976ad7969e0feddf%22%2C%22first_id%22%3A%22186d47d74351200-03057a6a9d2e908-57b1a33-2073600-186d47d7436114d%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTg2ZDQ3ZDc0MzUxMjAwLTAzMDU3YTZhOWQyZTkwOC01N2IxYTMzLTIwNzM2MDAtMTg2ZDQ3ZDc0MzYxMTRkIiwiJGlkZW50aXR5X2xvZ2luX2lkIjoiODkyMzA3YTIzZDRkNDAyZDk3NmFkNzk2OWUwZmVkZGYifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22892307a23d4d402d976ad7969e0feddf%22%7D%2C%22%24device_id%22%3A%22186d47d74351200-03057a6a9d2e908-57b1a33-2073600-186d47d7436114d%22%7D; _ga_L6XJCQTK75=GS1.1.1688298645.3.1.1688298663.42.0.0; RT="z=1&dm=www.mexc.com&si=8e953853-4601-4e8f-80e8-d3a8a5ea1a56&ss=ljld2dsx&sl=1&tt=1e7&bcn=%2F%2F17de4c12.akstat.io%2F&ld=d99z&ul=debz"', 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36'],
            'mexc1': ['mx0vglNAVEpdLCMSUk', '50c0e4ab359649609af9b561f88a9c81'],
            'mexc2': ['mx0vglZ5ANCN07D50O', 'd2833e9aef684a3aa26c3eac09bf4a0a'],
            'mexc3': ['mx0vglzwAMDo8Zz8wk', 'f2a833112073487781e4d47e4535d4e3'],
            'mexc4': ['mx0vgloHMZYXebl3BP', '6b07239aae31423d927ae03ad723a332'],
            '只读': ['mx0vglWOthgJ7teLb4', 'f4164330025346e9a85688417a76bfac'],
        }

        self.debug = 0
        self.debugs = []

        
        apiKey = apiKey if apiKey else Api_Key2
        if apiKey not in keys:
            apiKey = apiKey.split('	')
        else:
            apiKey = keys[apiKey].split('	') if type(keys[apiKey]) == str else keys[apiKey]
        self.access_id = apiKey[0]
        self.secret_key = apiKey[1]
        try:
            self.cookie = apiKey[2]
            self.ua = apiKey[3]
        except:
            self.cookie = 0
            self.ua = 0

        self.HOST = 'https://api.mexc.com'
        self.session = requests.Session()
        
        """ 缓存费率数据"""
        self.feilvData = {}
        self.symbol = {}
        self.user_data = 0
        self.tickerData = []

        """ 多出口IP"""
        try:
            ips = DuoIp
            # log('MexcSpot 使用多IP交易', ips)
        except:
            ips = []

        self.i = 0
        self.ips = []
        self.len_ips = len(ips)
        for ip in ips:
            from requests_toolbelt.adapters import source  #指定出口IP
            sb = requests.Session()
            new_source = source.SourceAddressAdapter(ip)
            sb.mount('http://', new_source)
            sb.mount('https://', new_source)
            self.ips.append(sb)

    
    def dispatch_request(self, method):
        return {
            'GET': self.session.get,
            'DELETE': self.session.delete,
            'PUT': self.session.put,
            'POST': self.session.post,
        }.get(method, 'GET')


    def _sign_v3(self, req_time, sign_params=None):
        if sign_params:
            sign_params = urlencode(sign_params)
            sign_params = sign_params.replace('+', '%20')
            to_sign = "{}&timestamp={}".format(sign_params, req_time)
        else:
            to_sign = "timestamp={}".format(req_time)
        sign = hmac.new(self.secret_key.encode('utf-8'), to_sign.encode('utf-8'), hashlib.sha256).hexdigest()
        return sign


    # 请求API
    def go(self, method, path, payload=None, needKey=1, headers=0, url=''):
        
        url = self.HOST + path if not url else url

        if self.ips:
            self.session = self.ips[self.i]
            self.i += 1
            if self.i >= self.len_ips:
                self.i = 0

        if not headers:
            headers = {
                'Content-Type': 'application/json'
            }

            if needKey:
                req_time = int(time.time()*1000)
                headers['x-mexc-apikey'] = self.access_id
                if payload:
                    payload['signature'] = self._sign_v3(req_time=req_time, sign_params=payload)
                else:
                    payload={}
                    payload['signature'] = self._sign_v3(req_time=req_time)
                    
                payload['timestamp'] = req_time
            params = {'url': url, 'timeout': 5, 'params': payload, 'headers': headers}
        
        else:
            headers = {
                'authority': 'www.mexc.com',
                'language': 'zh-CN',
                'content-type': 'application/json',
                'user-agent': self.ua,
                'accept': '*/*',
                'origin': 'https://www.mexc.com',
                'sec-fetch-site': 'same-origin',
                'sec-fetch-mode': 'cors',
                'sec-fetch-dest': 'empty',
                'referer': 'https://www.mexc.com/zh-CN/exchange/BTC_USDT?_from=search_spot_trade',
                'accept-language': 'zh-CN,zh;q=0.9',
                'cookie': self.cookie,
            }

            if method == 'POST':
                params = {'url': url, 'timeout': 5, 'data': ujson.dumps(payload), 'headers': headers}
            else:
                params = {'url': url, 'timeout': 5, 'params': payload, 'headers': headers}
            

        # print(params)
        if self.debug:
            t = NowTime_ms()

        # print(params)
        try:
            response = self.dispatch_request(method)(**params)
            if response.text:
                response2 = response.json()
            else:
                response2 = ''

        except Exception as e:
            traceback.print_exc()

            print('请求API失败', url)
            time.sleep(1)
            return self.go(method, path, payload, needKey)

        if self.debug:
            yc = NowTime_ms() - t
            self.debugs.append(yc)
            print(path, str(yc)+'ms', 'min:'+str(min(self.debugs)), 'max:'+str(max(self.debugs)), '均:'+str(round(sum(self.debugs)/len(self.debugs),2)))

        return response2


    """ 获取交易对规则"""
    def GetSymbols(self):
        return self.go("GET", '/api/v3/exchangeInfo', needKey=0)['symbols']

    

    """获取可用余额"""
    def GetYuer(self, p=1):
        all = self.GetPos(all=1)

        if not self.tickerData or tlog('MexcSpotTickers', '', 30, xs=0):
            self.tickerData = self.GetTickerAll()

        keyong = 0
        jiazhi = 0
        for pos in all:
            if pos['symbol'] == CcyBi2:
                keyong = pos['liang']

            else:
                for v in self.tickerData:
                    if pos['symbol'] == v['symbol'] and v['bidPrice']:
                        jiazhi += pos['liang'] * float(v['bidPrice'])
                        break
                else:
                    if '均衡' not in ServerName and '提现' not in ServerName:
                        tlog('Mexc Spot未找到价格', [pos, Color('', -1)], 10*60)
                    
        
        ok = {}
        ok['all'] = Si(jiazhi+keyong, 4)
        ok['keyong'] = Si(keyong, 4)
        ok['yingkui'] = 0
        
        ok['all'] = ok['all'] if ok['all'] else 0.01
        ok['keyong'] = ok['keyong'] if ok['keyong'] else 0.01

        if p:
            log(p+'  MexcSpot USDT总余额' if type(p) == str else 'MexcSpot USDT总余额', ok['all'], '可用', N(ok['keyong'], 4),
                 '持仓盈亏', N(ok['yingkui'], 4))
            
        return ok

    
    """ 获取所有持仓"""
    def GetPos(self, all=0):
        
        data = self.go("GET", "/api/v3/account")

        if not data or 'balances' not in data:
            print('MexcSpot 获取持仓失败', data)
            time.sleep(1)
            return self.GetPos(all)
        
        try:
            okdata = data['balances']
            data = []
            for v in okdata:
                if v['asset'] == CcyBi2 and not all:
                    continue
                
                
                ok = {}
                ok['symbol'] = v['asset']+'USDT' if v['asset'] != CcyBi2 else CcyBi2
                ok['liang'] = float(v['free'])
                if not ok['liang']:
                    continue

                ok['side'] = 'BUY'
                ok['side2'] = 'SELL'
                ok['jiage'] = 0
                ok['nowJiage'] = 0
                ok['yingkui'] = 0
                ok['bzj'] = 0
                ok['roe'] = 0
                ok['time'] = NowTime_ms()

                data.append(ok)

            # input('持仓')
            return data

        except Exception as e:
            uploadError(traceback.format_exc())
            
        log(Color("Mexc 获取持仓失败", -1), data)
        uploadLog(isExit=1)
        os._exit(0)



    """下单"""
    def PostOrder(self, symbol, side, jiage, liang, type='ioc', jiancang=0, msg2=''):
        

        jiage = str(jiage)

        if 'e' in jiage:
            jiage = str(round(Decimal(float(jiage)), 10))


        liang = str(liang)
        jiazhi = round(float(liang)*float(jiage),2)
        liang2 = liang+' ('+STR_N(jiazhi)+'$)'
        
        if not self.cookie:
            msg = 'Mexc现货　'+symbol+'　'
        else:
            msg = 'MexcWeb现货　'+symbol+'　'

        if side == 'BUY':
            msg += '开多仓'
        else:
            msg += '平多仓'

        msg += "　方向:"+side+"　价格:"+jiage+"　量:"+liang2+"　Type:"+type

        if jiazhi < 5:
            return 0, msg+' 价值小于5u'

        if not self.cookie:
            post = {
                'symbol': symbol,
                'side': side,
                'type': 'IMMEDIATE_OR_CANCEL' if type == 'ioc' or type == 'normal' else type,
                'quantity': liang,
                'price': jiage,
            }
        
        else:
            s = symbol.replace('USDT', '')
            post = {
                "currency": s,
                "market":"USDT",
                "tradeType": side,
                "price": float(jiage),
                "quantity": liang if side == 'SELL' else str(jiazhi),
                "orderType":"MARKET_ORDER"
            }
            print(post)

        #市价单，BUY 要填 quoteOrderQty
        #部分交易对不支持Api市价单
        # if type == 'normal':
        #     post['type'] = 'MARKET'
        #     del post['price']


        orderId = 0
        
        t = NowTime_ms()

        for x in range(1):

            if not self.cookie:
                order = self.go('POST', "/api/v3/order", post)

                # log("下单返回", order)
                if not order or 'orderId' not in order:
                    log(symbol+" [!!!] 套利下单失败！！重试", post, order)

                    # if 'code' not in order or order['code'] not in ['429000', '300003', '300007']:    #300003 余额不足  300007 标记价格过远
                    # uploadError(symbol+' MexcSpot下单失败：'+str(post)+'  '+str(order))

                else:
                    orderId = order['orderId']
                    break
            
            else:
                order = self.go('POST', '', post, headers=1, url='https://www.mexc.com/api/platform/spot/order/place')

                # log("下单返回", order)
                if not order or 'data' not in order:
                    log(symbol+" [!!!] 套利下单失败！！重试", post, order)

                else:
                    orderId = order['data']
                    break
        
        msg = [msg+ '　'+ Color(msg2, 1), str(NowTime_ms()-t)+'ms', orderId]

        return orderId, msg


    """ 撤销交易对下的订单"""
    def DelSymbolOrder(self, symbol):
        post = {
            'symbol': symbol,
        }

        t = NowTime_ms()

        order = self.go('DELETE', "/api/v3/openOrders", post)

        msg = [symbol, '撤单', str(NowTime_ms()-t)+'ms', order]
        print(*msg)



    """ 获取Userid"""
    def GetUserId(self, name):
        if not self.user_data:
            self.user_data = self.go("GET", "/api/v1/sub/user")['data']

        for v in self.user_data:
            if v['subName'] == name:
                log('MexcSpot', name, 'userid', v['userId'])
                return v['userId']

        log('未找到此用户', name, self.user_data)
        return 0




    """获取现货余额"""
    def GetXianhuo(self, bi=''):
        bi = bi if bi else CcyBi
        data = self.go("GET", "/api/v1/accounts", {'currency': bi})
        for v in data['data']:
            if v['type'] == 'main':
                return N(v['available'], 4)
        log('Ku 现货未找到', data)
        return 0.001


    """ 生成Key"""
    def CreateKey(self):
        fh = self.go('POST', '/api/v3/userDataStream')['listenKey']
        log('MexcSpot获取WsKey', fh)
        return fh


    """ 生成Key"""
    def PutKey(self, key):   
        fh = self.go('PUT', '/api/v3/userDataStream', {'listenKey': key})
        log('MexcSpot延长Key', key, fh)
        return fh
    

    """ 获取所有交易对价格、挂单盘口"""
    def GetTickerAll(self, bi=''):
        data = self.go("GET","/api/v3/ticker/bookTicker", {}, needKey=0)
        if len(data) < 10:
            log('MexcSpot 获取失败', data)
            time.sleep(3)
            return self.GetTickerAll()
        
        if bi:
            jiage = 0
            for v in data:
                if bi == v['symbol']:
                    jiage = float(v['askPrice'])
            return jiage
            
        return data


    """ 子母账户相互转账"""
    def ziHua(self, liang, faWallet, jieWallet, fa='', jie='', bi=''):

        for i in range(5):
            bi = bi if bi else CcyBi
            data = {
                'fromAccountType': faWallet,    #"SPOT","FUTURES","ISOLATED_MARGIN"
                'toAccountType': jieWallet, 
                'asset': bi,
                'amount': liang,
            }
            if fa:
                data['fromAccount'] = fa
            if jie:
                data['toAccount'] = jie

            data = self.go("POST", "/api/v3/capital/sub-account/universalTransfer", data)
            msg = ['[MexcSpot万向划转]', '发送者', fa, faWallet, '接受者', jie, bi, jieWallet, '币种', bi, liang, '结果', data]
            log(*msg)

            if 'tranId' in data:
                return data['tranId']

            uploadError('MexcSpot万向划转失败！'+str(msg))
            time.sleep(2)

        return 0
    


    """创建子账户"""
    def CreateNumber(self, username):
        data = self.go("POST","/api/v1/sub/user", {'password': "Kekenb888", 'subName': username, 'access': 'All'})
        log('MexcSpot 创建子账户', username, '返回', data)
        return data



    """创建子账户"""
    def CreateFuturesApi(self, username, ip):
        p = {
            'subName': username,
            'passphrase': self.passwd,
            'remark': username,
            'ipWhitelist': ip,
            'permission': 'General,Trade',
        }
        data = self.go("POST","/api/v1/sub/api-key", p)
        log('MexcSpot 创建子账户Api', p, '返回', data)
        
        return data['data']


    """ 获取WS URL"""
    def GetWsUrl(self, auth=0):

        if auth:
            url = '/api/v1/bullet-private'
        else:
            url = '/api/v1/bullet-public'
        
        res = self.go("POST", url, {})

        if res["code"] == "200000":
            token = res["data"]["token"]
            ws_connect_id = str(uuid4()).replace('-', '')
            endpoint = res["data"]['instanceServers'][0]['endpoint']
            ws_endpoint = f"{endpoint}?token={token}&connectId={ws_connect_id}"
            encrypt = res["data"]['instanceServers'][0]['encrypt']
            if auth:
                ws_endpoint += '&acceptUserMessage=true'
                
            return ws_endpoint
        else:
            raise Exception("MexcSpot usdt swap 获取token错误")


    """提现"""
    def Tixian(self, dizhi, liang, bi='USDT', net='Tron(TRC20)'):

        # data = self.go('GET', '/api/v3/capital/config/getall')
        # for v in data:
        #     # print(v['coin'])
        #     if v['coin'] == 'USDT':
        #         pprint(v)
        
        data = {'coin':bi, 'address':dizhi, 'amount':liang, 'network': net}
        data = self.go("POST", "/api/v3/capital/withdraw/apply", data)
        log('Mexc 提现 地址', dizhi, '数量', liang, '链', net, '币种', bi, '结果', data)  
        return data