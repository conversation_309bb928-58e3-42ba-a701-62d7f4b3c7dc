#!/usr/bin/python
# -*- coding: utf-8 -*-
from __future__ import unicode_literals
from urllib import request
import urllib3
import urllib
from urllib3.exceptions import InsecureRequestWarning

urllib3.disable_warnings(InsecureRequestWarning)
http = urllib3.PoolManager(timeout=urllib3.Timeout(connect=1, read=2))

import time
import ujson
import traceback        #输出报错
from pprint import pprint
from urllib import parse
import json
from datetime import datetime
import hmac
import base64
import hashlib
from uuid import uuid4


from main.config import *
from main.hanshu import *

# try:
#     import rusthttp_py.adapter as requests
# except:
#     uploadError("未安装RustSDK")
import requests

def parse_params_to_str(params):
    url = '?'
    for key, value in params.items():
        url = url + str(key) + '=' + str(value) + '&'
    return url[0:-1]

    
class kucoin():

    def __init__(self, apiKey=0, ccyBi=0, symbols={}):
        keys = {
            '吃了吗资本': ['638b5dd6168a420001e8bdb9', '4f52ba72-8e04-4392-bedc-f89d6d286305', 'b87d055f'],
            '吃了吗资本Spot': ['638b669589f46c0001d7cda0', 'a49cf518-e6ba-48bf-a778-6bb946fe3830', 'b87d055f'],
            'ku1': ['638c6227ce03a70001f6d5e5', 'a244a92d-6f1d-494e-87e8-83ef651c7259', 'b87d055f'],
            'ku2': ['638c1ce815cfb300010b5736', '34f618b4-6b5d-4c32-825a-49dbe122d9f5', 'b87d055f'],
            'ku3': ['638c1ce81ebe670001ccfb71', '48aebd10-70c7-422c-bb89-b4bdd4c4948c', 'b87d055f'],
            'ku4': ['638c1ce827af8900017af8d2', 'bb95d42c-56c4-4be1-9e32-40767f88b2fd', 'b87d055f'],
            'ku5': ['638b6ac7b0e90e000145abb2', 'f7ab0c90-59e3-470f-a958-e6ba0ca92db7', 'b87d055f'],        #子账户是rich1
            'ku6': ['6391f94523d118000122d47c', '6eda8648-e486-4526-8dc9-a3525cbdaa8d', 'b87d055f'],        #子账户是clmniubi6
            'ku7': ['6391f94581bf0f000105c815', '1c631010-f25a-4b9f-8934-b456f7da5c91', 'b87d055f'],
            'ku8': ['6391f945fb65340001670c85', 'db57fbb7-ae70-402c-a54b-ed0053d7d1e7', 'b87d055f'],
            'ku9': ['6391f945cc568b00012797a9', '9ed6def6-1801-4f6c-8b0d-03dc02282114', 'b87d055f'],
            'ku10': ['6391f94581bf0f000105c819', 'b8df0777-0f89-4a38-be06-6216518da95d', 'b87d055f'],
            'ku11': ['6426830afc1e8900014cb5f9', 'fa38f81b-6377-4a0a-8eea-faa5cd53655d', 'b87d055f'],   #clmniubi11
            'ku12': ['6391f9462641e100010327ff', '7d768981-0a44-483a-8b0b-7fa66ffb01db', 'b87d055f'],
            'ku13': ['6391f9465eb5a30001ccd0b6', 'b34f7dd9-354f-4538-92a5-2cea0404f42f', 'b87d055f'],
            'ku14': ['6391f9468deb6c0001df5eb0', '3d133a7a-cd47-4943-96e5-de679d7120ad', 'b87d055f'],
            'ku15': ['6391facd2641e10001032ace', '71da71e8-66f2-45ac-9b0a-cd6974dc283f', 'b87d055f'],

            'rich4': ['6393f35417307f00015763b2', '95fce629-923c-47cb-9e3f-8ac3d410e197', 'b87d055f'],
            'rich5': ['6393f354a27b33000130f30b', 'fd169dfc-be69-4e89-ab27-a43af186fe73', 'b87d055f'],
            'rich6': ['6393f3542641e1000106297b', 'b470d600-2285-4b52-ae5f-c75d220a3ec5', 'b87d055f'],
            'rich7': ['6393f35481bf0f000108bfd1', '2c84fe0c-3f43-4daf-b9a4-4b4c498c97db', 'b87d055f'],
            'rich20': ['6393f356a0afa60001152eb5', 'e8968d7c-164c-438b-ab66-8adcb4027243', 'b87d055f'],

        }

        self.debug = 0
        self.debugs = []

        apiKey = apiKey if apiKey else Api_Key2
        self.access_id = keys[apiKey][0]
        self.secret_key = keys[apiKey][1]
        self.passwd = keys[apiKey][2]

        self.session = requests.Session()
        
        """ 缓存费率数据"""
        self.feilvData = {}
        self.symbol = symbols
        self.user_data = 0

        """ 多出口IP"""
        try:
            ips = DuoIp
            if len(ips) > 1:
                log(f'Kucoin 使用多IP交易', ips)
        except:
            ips = []

        self.i = 0
        self.ips = []
        self.len_ips = len(ips)
        for ip in ips:
            from requests_toolbelt.adapters import source  #指定出口IP
            sb = requests.Session()
            new_source = source.SourceAddressAdapter(ip)
            sb.mount('http://', new_source)
            sb.mount('https://', new_source)
            self.ips.append(sb)

    
    def dispatch_request(self, method):
        return {
            'GET': self.session.get,
            'DELETE': self.session.delete,
            'PUT': self.session.put,
            'POST': self.session.post,
        }.get(method, 'GET')


    def get_sign(self, timestamp, method, request_path, params, secret_key):
        if params == None:
            params_str = ""
        else:
            if method == 'POST':
                params_str = ujson.dumps(params)
            else:
                params_str = parse_params_to_str(params)

        message = str(timestamp) + str.upper(method) + request_path + params_str
        mac = hmac.new(bytes(secret_key, encoding='utf-8'), bytes(message, encoding='utf-8'), digestmod='sha256').digest()
        return str(base64.b64encode(mac), 'utf-8')


    # 请求API
    def go(self, method, path, payload=None, needKey=1, spot=0):
        
        if spot:
            url = "https://api.kucoin.com" + path
            
        else:
            url = "https://api-futures.kucoin.com" + path

        if self.ips:
            self.session = self.ips[self.i]
            self.i += 1
            if self.i >= self.len_ips:
                self.i = 0

        headers = {}
        if needKey:
            now_time = int(time.time()) * 1000
            str_to_sign = str(now_time) + method + path
            if method in ['GET', 'DELETE']:
                data_json = ''
                if payload:
                    strl = []
                    for key in payload:
                        strl.append("{}={}".format(key, payload[key]))
                    data_json += '&'.join(strl)
                    str_to_sign += '?' + data_json
            elif payload:
                str_to_sign += ujson.dumps(payload)

            sign = base64.b64encode(hmac.new(self.secret_key.encode('utf-8'), str_to_sign.encode('utf-8'), hashlib.sha256).digest())
            passphrase = base64.b64encode(hmac.new(self.secret_key.encode('utf-8'), self.passwd.encode('utf-8'), hashlib.sha256).digest())
            headers = {
                "KC-API-SIGN": sign.decode(),
                "KC-API-TIMESTAMP": str(now_time),
                "KC-API-KEY": self.access_id,
                "KC-API-PASSPHRASE": passphrase.decode(),
                "Content-Type": "application/json",
                "KC-API-KEY-VERSION": "2"
            }
            

        headers["User-Agent"] = "kucoin-python-sdk/v1.0"


        if method == 'POST':
            params = {'url': url, 'timeout': 5, 'headers': headers}
            if payload:
                params['data'] = ujson.dumps(payload)
        else:
            params = {'url': url, 'timeout': 5, 'params': payload, 'headers': headers}


        if self.debug:
            t = NowTime_ms()

        # print(params)
        try:
            response = self.dispatch_request(method)(**params)
            if response.text:
                response2 = response.json()
            else:
                response2 = ''

        except Exception as e:
            traceback.print_exc()

            print('请求API失败', url)
            time.sleep(1)
            return self.go(method, path, payload, needKey)

        if self.debug:
            yc = NowTime_ms() - t
            self.debugs.append(yc)
            print(method, path, str(yc)+'ms', 'min:'+str(min(self.debugs)), 'max:'+str(max(self.debugs)), '均:'+str(round(sum(self.debugs)/len(self.debugs),2)))

        return response2



    """ 获取所有交易对资金费率"""
    def GetSymbolFeilv(self, symbol=0):
        if not self.feilvData or not symbol:
            data = self.go("GET", '/api/v1/contracts/active', {}, needKey=0)
        
            if not data or 'data' not in data:
                log('Ku获取资金费率失败', data)
                time.sleep(0.1)
                return self.GetSymbolFeilv(symbol)

            data = data['data']
            if not symbol:
                return data

            for v in data:
                if v['nextFundingRateTime']:
                    v['nextFundingRateTime'] =  NowTime() + int(v['nextFundingRateTime'] / 1000)
                    v['funding_time'] = NowTime()
                    self.feilvData[v['symbol']] = v

        try:
            symbolData = self.feilvData[symbol]
        except:
            symbolData = 0

        if not symbolData or NowTime() >= symbolData['nextFundingRateTime']+2:
            log(symbol, '更新资金费率',
                 '更新时间', GetTime(symbolData['nextFundingRateTime'], '%m-%d %H:%M:%S') if symbolData else '',
                 '获取时间', GetTime(symbolData['funding_time'], '%m-%d %H:%M:%S') if symbolData else ''
                 )

            self.feilvData = {}
            time.sleep(1)
            return self.GetSymbolFeilv(symbol)

        else:
            # print(NowTime_ms(), symbolData['nextFundingTime'])
            # print(symbol, '获取资金费率',
            #      '更新时间', GetTime(symbolData['nextFundingTime'], '%m-%d %H:%M:%S') if symbolData else '',
            #      '获取时间', GetTime(symbolData['time'], '%m-%d %H:%M:%S') if symbolData else ''
            #      )
            return round(float(symbolData['fundingFeeRate'])*100, 4)


    """ 获取交易对规则"""
    def GetSymbols(self):
        return self.go("GET", '/api/v1/contracts/active', {}, needKey=0)['data']


    """获取可用余额"""
    def GetYuer(self, p=1):
        data = self.go("GET", "/api/v1/account-overview", {'currency': CcyBi})
        
        if not data or 'data' not in data or 'accountEquity' not in data['data']:
            print('Kucoin 获取余额失败', self.access_id, data)
            time.sleep(1)
            return self.GetYuer(p)

        okk = data['data']
        
        ok = {}
        ok['all'] = N(okk['accountEquity'], 4)
        ok['keyong'] = N(okk['availableBalance'], 4)
        ok['yingkui'] = N(okk['unrealisedPNL'], 4)
        
        ok['all'] = ok['all'] if ok['all'] else 0.01
        ok['keyong'] = ok['keyong'] if ok['keyong'] else 0.01

        if p:
            log('Kucoin USDT总余额', ok['all'], '可用', N(ok['keyong'], 4), '持仓盈亏', N(ok['yingkui'], 4))

        return ok


    
    """ 获取所有持仓"""
    def GetPos(self):
        data = self.go('GET', "/api/v1/positions", {})
        # print('持仓', data)
        try:
            if 'data' in data and 'code' in data and data['code'] == '200000':
                okdata = data['data']
                data = []
                for v in okdata:
                    ok = {}
                    ok['symbol'] = v['symbol']
                    ok['liang'] = abs(v['currentQty'])

                    ok['side'] = 'BUY' if v['currentQty'] > 0 else 'SELL'
                    ok['side2'] = 'SELL' if v['currentQty'] > 0 else 'BUY'

                    ok['jiage'] = float(v['avgEntryPrice'])
                    ok['nowJiage'] = float(v['markPrice'])    #最新成交价格
                    ok['yingkui'] = N(float(v['unrealisedPnl']), 4)
                    ok['bzj'] = N(float(v['posMargin']), 4)
                    ok['roe'] = float(v['unrealisedRoePcnt'])
                    ok['time'] = NowTime_ms()

                    data.append(ok)

                # input('持仓')
                return data

            else:
                log(Color("获取持仓失败", -1), data)
                time.sleep(3)
                return self.GetPos()

        except Exception as e:
            uploadError(traceback.format_exc())
        
        
        log(Color("获取持仓失败", -1), data)
        uploadLog(isExit=1)
        os._exit(0)



    """下单"""
    def PostOrder(self, symbol, side, jiage, liang, type='ioc', jiancang=0, msg2=''):
        jiage = str(jiage)
        liang = str(liang)
        GG = self.symbol[symbol]['GangGan'] if symbol in self.symbol else 5
        MZ = self.symbol[symbol]['Z'] if symbol in self.symbol else 1
        liang2 = liang+' ('+STR_N(float(liang)*float(jiage)*MZ)+'$)'
        # print(GG)
        

        msg = 'Kucoin　'+symbol+'　'

        if jiancang:
            msg += '平空仓' if side == 'BUY' else '平多仓'

            if symbol == 'TOMOUSDTM':
                GG = 3

        else:
            msg += '开多仓' if side == 'BUY' else '开空仓'

        side = side.lower()
        msg += "　方向:"+side+"　价格:"+jiage+"　量:"+liang2+"　面值:"+str(MZ)+"　减仓:"+str(jiancang)+"　Type:"+type

        post = {
            'clientOid': str(uuid4()),
            'side': side,
            'symbol': symbol,
            'type': 'limit',
            'leverage': GG,
            'price': jiage,
            'size': liang,
            'timeInForce': type.upper(),
        }

        if type == 'normal':
            post['type'] = 'market'
            del post['timeInForce']

        if type == 'limit':
            post['timeInForce'] = 'GTC'

        if type == 'post_only':
            post['timeInForce'] = 'GTC'
            post['postOnly'] = True

        orderId = 0
        
        t = NowTime_ms()

        for x in range(1):
            order = self.go('POST', "/api/v1/orders", post)

            # log("下单返回", order)
            if not order or 'data' not in order or not order['data'] or 'orderId' not in order['data']:
                #300003 余额不足  300007 标记价格过远    300012 最低价格不能低于
                #200002 请求过多  300005 持仓上限
                # if 'code' not in order or order['code'] not in ['429000', '300003', '300007', '300012', '200002', '300005']:    
                log(symbol+" [!!!] 套利下单失败！！重试", post, order)
                #     uploadError(symbol+' Kucoin下单失败：'+str(post)+'  '+str(order))
                #     break
                
                # time.sleep(0.1)
                
            else:
                orderId = order['data']['orderId']
                break
        
        msg = [msg+ '　'+ Color(msg2, 1), str(NowTime_ms()-t)+'ms', orderId]
        
        return orderId, msg


    """ 撤单"""
    def DeleteOrder(self, symbol, id):
        
        fh = self.go('DELETE', f'/api/v1/orders/{id}', {})
        log('Kucoin 撤单', symbol, id, fh)
        return fh


    """ 全部撤单"""
    def DeleteAllOrder(self, symbol):
        
        fh = self.go('DELETE', f'/api/v1/orders?symbol={symbol}', {})
        log('Kucoin 全部撤单', symbol, fh)
        return fh



    """ 开启自动追加保证金"""
    def SetBzj(self, symbol):
        fh = self.go('POST', '/api/v1/position/margin/auto-deposit-status', {'symbol': symbol, 'status': True})
        print('Kucoin 开启追加保证金', symbol, fh)
        return fh
        
    
    """设置杠杆倍数"""
    def SetGangGan(self, symbol, beishu):
        beishu = str(int(beishu))
        level, m = self.GetMaxPos(symbol, getLevel=1)
        
        fh = self.go("POST","/api/v1/position/risk-limit-level/change",{'symbol': symbol,'level': level})

        if not self.ips:
            time.sleep(0.1)

        print(symbol, 'Ku设置杠杠', beishu, '-->', '等级', level, '最大可开', U(m), fh)


    """ 获取最大最大可开数量，返回最大价值"""
    def GetMaxPos(self, symbol, getLevel=0):
    
        fh = self.go("GET", f"/api/v1/contracts/risk-limit/{symbol}")

        if not fh or not fh['data']:
            return 9999999

        for v in fh['data']:
            if v['maxLeverage'] <= SetGangGan:

                m = v['minRiskLimit'] if v['minRiskLimit'] else v['maxRiskLimit']
                if getLevel:
                    return v['level'], m

                print('Kucoin', symbol, '杠杆', v['maxLeverage'], '最大可开', U(m))
                return m


    """ 获取Userid"""
    def GetUserId(self, name):
        if not self.user_data:
            self.user_data = self.go("GET", "/api/v1/sub/user", spot=1)['data']

        for v in self.user_data:
            if v['subName'] == name:
                # log('Kucoin', name, 'userid', v['userId'])
                return v['userId']

        log('未找到此用户', name, self.user_data)
        return 0


    """提现"""
    def Tixian(self, dizhi, liang, bi='USDT', net='TRC20'):
        data = self.go("POST", "/api/v1/withdrawals", {'currency':bi, 'address':dizhi, 'amount':liang, 'chain': net}, spot=1)
        log('Kucoin 提现 地址', dizhi, '数量', liang, '币种', bi, '结果', data)  
        return data

    """获取现货余额"""
    def GetXianhuo(self, bi=''):
        bi = bi if bi else CcyBi
        data = self.go("GET", "/api/v1/accounts", {'currency': bi}, spot=1)
        if 'data' not in data:
            return 0.0001
        for v in data['data']:
            if v['type'] == 'main':
                return N(v['available'], 4)
        log('Ku 现货未找到', data)
        return 0.001
    

    """ 子母账户相互转账"""
    def ziHua(self, liang, uid, side, bi='', spot=0):
        
        bi = bi if bi else CcyBi
        data = {
            'clientOid': str(uuid4()),
            'currency': bi, 
            'amount': str(liang),
            'subUserId': uid,
            'direction': 'OUT' if side == 'in' else 'IN',
            'accountType': 'MAIN',
            'subAccountType': 'CONTRACT' if side == 'in' else 'MAIN',
        }
        if spot:
            data['subAccountType'] = 'TRADE_HF'

        data = self.go("POST", "/api/v2/accounts/sub-transfer", data, spot=1)
        print(data)
        log('[Kucoin子母]', '主账户转入' if side == 'in' else '子账户转出', '币种', bi, liang, '结果', data, Color('', 1))

        return 'code' in data and data['code'] == '200000'


    """ 合约划转出去"""
    def huazhuan(self, liang, bi='', side='out'):
        bi = bi if bi else CcyBi
        if side == 'out':
            data = {
                'amount': liang,
                'currency': bi,
                'recAccountType': 'MAIN',
            }
            data = self.go("POST", "/api/v3/transfer-out", data)

            log('[Kucoin]', '子账户合约划转到现货', '币种', bi, liang, '结果', data, Color('', 1))

            fh = 'code' in data and data['code'] == '200000'
            if not fh:
                uploadError(str(data))
            return fh

        else:
            data = {
                'amount': liang,
                'currency': bi,
                'payAccountType': 'MAIN',
            }
            data = self.go("POST", "/api/v1/transfer-in", data)

            log('[Kucoin]', '子账户现货划转回去', '币种', bi, liang, '结果', data, Color('', 1))

            return 'code' in data and data['code'] == '200000'



    """创建子账户"""
    def CreateNumber(self, username, passwd="Kekenb888"):
        data = self.go("POST","/api/v1/sub/user", {'password': passwd, 'subName': username, 'access': 'All'}, spot=1)
        log('Kucoin 创建子账户', username, '返回', data)
        return data



    """创建子账户"""
    def CreateFuturesApi(self, username, ip):
        p = {
            'subName': username,
            'passphrase': self.passwd,
            'remark': username+'-3',
            'ipWhitelist': ip,
            'permission': 'General,Trade',
        }
        data = self.go("POST","/api/v1/sub/api-key", p)
        log('Kucoin 创建子账户Api', p, '返回', data)
        
        return data['data']


    """ 获取WS URL"""
    def GetWsUrl(self, auth=0):

        if auth:
            url = '/api/v1/bullet-private'
        else:
            url = '/api/v1/bullet-public'
        
        res = self.go("POST", url, {})

        if res["code"] == "200000":
            token = res["data"]["token"]
            ws_connect_id = str(uuid4()).replace('-', '')
            endpoint = res["data"]['instanceServers'][0]['endpoint']
            ws_endpoint = f"{endpoint}?token={token}&connectId={ws_connect_id}"
            encrypt = res["data"]['instanceServers'][0]['encrypt']
            if auth:
                ws_endpoint += '&acceptUserMessage=true'
                
            return ws_endpoint
        else:
            raise Exception("kucoin usdt swap 获取token错误")
    


    def GetSpotTick(self):
        return self.go("GET", "/api/v1/market/allTickers", spot=1)