from main.hanshu import *
from main.config import *
from moneyV2.money import *
from moneyV2.table import *
from moneyV2.symbol import *

import random

def DeleteOrder(self, symbol):
    if self.preJia:
        self.bnb.DeleteAllOrder(symbol)
        self.preJia = 0


""" 现货买入卖出"""
def Spot(self, symbol, data, isbnb=0):
    
    J, L, Max, Min, MZ         = self.symbol['J'], self.symbol['L'], self.symbol['M']*0.99, self.symbol['Min'], self.symbol['Z']

    bid, ask = float(data['bidPx']), float(data['askPx'])
    bidQty, askQty = float(data['bidQty']), float(data['askQty'])

    pankou = [
        symbol, '延迟', NowTime_ms()-data['t'], '',
        'bid', str(bid)+' ('+str(bidQty)+')'+' ('+U(bidQty*bid*MZ)+')', 'ask', str(ask)+' ('+str(askQty)+')'+' ('+U(askQty*ask*MZ)+')'
    ]

    """ 持仓价值"""
    jiazhi = 0
    pos  = GetNewPos(self, Exchange, symbol, '', bid, MZ, L)
    if pos:
        jiazhi = round(pos['liang']*ask, 2)
        if self.prePos != pos['liang']:
            tlog('持仓价值', [U(jiazhi, 1)], 3)
            self.prePos = pos['liang']

    getCaokongData(self, toSymbol(symbol).replace(CcyBi, ''), data, pos, 0)

    if not self.pos_limit:
        return tlog('未设置持仓限制...', '', 60)

    if 'stop' == self.side:
        DeleteOrder(self, symbol)
        if tlog(f'{Name} 策略已暂停...', '', 60):
            yuer = self.bnb.GetYuer(p=0)
            if yuer['jiekuan'] > 1:
                self.bnb.HuanKuan(Si(yuer['jiekuan'], 6))

    elif 'open' in self.side:

        """ 推动上涨"""

        if not self.usdt:
            return tlog('没U', [self.bnb.GetYuer(p=0)], 3)

        if self.stopOpen:
            return tlog('任务已完成 等待指令', '', 30)
        
        taker = min(self.usdt * 0.8, self.pos_limit)
        limit = (self.usdt - taker) * 0.85

        if jiazhi >= taker-11:
            DeleteOrder(self, symbol)
            tlog('持仓达标了', ['持仓', U(jiazhi, 1), '总可开', U(self.usdt), '目标', U(taker)], 3)
            self.stopOpen = 1
            return

        """ 挂买1"""
        if 'Buy' in self.side and bid != self.preJia:
            liang = N(limit / bid, L)
            if liang * bid > 100:
                if liang > Max:
                    liang = N(Max, L)
                    log('超过最大下单量', Max, U(Max*bid))

                DeleteOrder(self, symbol)
                order_id, msg = self.bnb.PostOrder(symbol, 'BUY', bid, liang, 'post_only')
                if order_id:
                    self.preJia = bid
                    self.preLiang = liang
                    if not self.SujiTime:
                        self.SujiTime = NowTime_ms() + 3000

        """ 不断下单推动"""
        if NowTime_ms() >= self.SujiTime:
            duishou = askQty * ask if 'Buy' in self.side else bidQty * bid
            side = 'BUY' if 'Buy' in self.side else 'SELL'
            if self.takerCount <= 2:
                waitTime = random.randint(1000, 4000)
                liang = min(duishou * 0.2, 2000)

            else:
                waitTime = random.randint(1000, 6000)
                liang = min(duishou, random.randint(400, 700))

            shengyu = round(taker - jiazhi, 1)
            liang = min(liang, shengyu)
            liang = max(liang, 11)

            print(f'--------第 {self.takerCount} 次--------')
            print('对手', U(duishou), 
                  ' ',
                  '目标', U(taker), '持仓', U(jiazhi), '剩余', U(shengyu), 
                    '-------> 本次下单', U(liang),'\n')

            DeleteOrder(self, symbol)
            order_id, msg = self.bnb.PostOrder(symbol, side, bid, N(liang / bid, L), 'normal')
            if order_id:
                self.takerCount += 1
            
            self.SujiTime = NowTime_ms() + waitTime
            print('等待', round(waitTime / 1000, 1), 's\n')

            Sleep(100)
            upPos(self, set=1)

            if order_id:
                for i in range(1, 200): #2s
                    if order_id in self.ws.orders:
                        order = self.ws.orders[order_id]
                        if order['status'] in ['FILLED', 'EXPIRED']:
                            addPos(self, symbol, order['jiage'], order['liang'], J, side)
                            break

                    time.sleep(0.01)

    else:

        """ 清理所有仓位"""
        if pos and pos['liang'] * bid > 11:
            
            if 'closeAll' == self.side:
                """ 一次性"""
                # 撤销上次挂单
                DeleteOrder(self, symbol)

                x = 0.0015
                jiage = bid - (bid * x) if pos['side'] == 'BUY' else ask + (ask * x)
                jiage = N(jiage, J)
                jiage2 = bid  if pos['side'] == 'BUY' else ask 

                log(pos['side'], jiazhi, '平仓', '当前价', jiage2, '偏移价', jiage, StrBi(x, ))

                self.bnb.PostOrder(symbol, pos['side2'], jiage, pos['liang'], 'limit', 1)
                self.preJia = bid

                Sleep(5000)
                upPos(self, set=1)

            else:
                """ 缓慢砸盘"""
                if NowTime_ms() >= self.SujiTime:
                    liang = random.randint(600, 1500)
                    duishou = bidQty * bid  if pos['side'] == 'BUY' else askQty * ask

                    """ 每6次吃一次对手盘"""
                    if self.takerCount and self.takerCount % 6 == 0:
                        liang = max(duishou, 2000)

                    print(f'--------第 {self.takerCount} 次--------')
                    print('对手', U(duishou), 
                        ' ',
                        '持仓', U(jiazhi), 
                            '-------> 本次砸盘', U(min(jiazhi, liang)),'\n')

                    liang = N(liang / bid, L)
                    liang = min(liang, pos['liang'])

                    order_id, msg = self.bnb.PostOrder(symbol, pos['side2'], bid, liang, 'normal')
                    if order_id:
                        self.takerCount += 1
                        self.preJia = bid
                    
                    waitTime = random.randint(2000, 5500)
                    self.SujiTime = NowTime_ms() + waitTime
                    print('等待', round(waitTime / 1000, 1), 's\n')

                    # Sleep(500)
                    # upPos(self, set=1)


        else:
            if tlog('平仓完毕', pos, 60):
                delPos(self, symbol, 'BUY')
                delPos(self, symbol, 'SELL')
                time.sleep(5)
                upPos(self, set=1)
                