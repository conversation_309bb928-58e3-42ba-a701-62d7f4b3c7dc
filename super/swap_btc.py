from main.hanshu import *
from main.config import *
from moneyV2.money import *
from moneyV2.table import *
from moneyV2.symbol import *

import random
import fasthttp
import ujson



""" 根据持仓量和挂单量计算可用资金"""
def getKeyong(name,  ws, side, posGg, pos, orders):
    zhanyong = 0
    for orderId in orders:
        order = orders[orderId]
        if order['side'] == side and order['name'] == name:
            zhanyong += order['liang'] * order['jiage']
    
    if pos and  pos['side'] == side:
        zhanyong += pos['jiazhi']

    zhanyong = zhanyong / posGg
    zijiKeyong = ws.usdt - zhanyong
    keyong = ws.keyong

    if zijiKeyong < keyong:
        tlog(name, ['总余额', ws.usdt, '自己计算占用保证金', zhanyong, '计算后余额', zijiKeyong, 'WS可用保证金', keyong], 60)
        keyong = ziji<PERSON><PERSON>ong
    return keyong


""" 合约买入卖出"""
def Swap(self, symbol, data, isbnb=0):


    mult = 0.98
    
    J, J2, L, Min, MZ      = self.symbol['J'], self.symbol['J2'], self.symbol['L'], self.symbol['Min'], self.symbol['Z']
    posGg = self.ganggan

    bid, ask = float(data['bidPx']), float(data['askPx'])
    bidQty, askQty = float(data['bidQty']), float(data['askQty'])
    mid = (bid + ask) * 0.5


    maxPos = MAX

    """ 最大市价单"""
    # if self.symbol['M'] and self.symbol['M']:
    #     maxPos = self.symbol['M']*0.99

    """ 持仓上限制"""
    if 'maxJiazhi' in self.symbol and self.symbol['maxJiazhi']:
        maxPos  = min(maxPos, N(self.symbol['maxJiazhi'] / mid, L))       #最大持仓量
    
    if 'maxLiang' in data and data['maxLiang']:
        maxPos = min(maxPos, data['maxLiang'])
    
    maxPos = maxPos * mult

    ttt = []
    for orderId in self.orders:
        order = self.orders[orderId]
        price = randPrice(order['side'], bid, ask, J, check=order['jiage']) # 
        if price:
            newLiang = N(order['jiazhi'] / price, L)
            ttt.append(order['rest'].EditOrder(order['side'], symbol, price, newLiang, orderId, 1))

    if ttt:
        t = NowTime_ms()
        responses = fasthttp.batch_request(ttt)
        log('批量修改订单', NowTime_ms() - t, 'ms')
        for data in responses:
            try:
                editData = ujson.loads(data)
            except:
                editData = 0
            if editData and 'orderId' in editData: 
                orderId = editData['orderId']
                if self.orders and orderId in self.orders:
                    # if editData['orderId'] != orderId:
                    #     self.orders[orderId] = self.orders[editData['orderId']].copy()
                    price, liang = float(editData['price']), float(editData['origQty'])
                    self.orders[orderId]['jiage'] = price
                    self.orders[orderId]['liang'] = liang
                    self.orders[orderId]['jiazhi'] = N(price * liang, 2)
                    # log("修改后的订单：", self.orders[orderId])
                    # if editData['orderId'] != orderId:
                    #     del self.orders[editData['orderId']]

            else:
                log('修改订单失败', data)
                self.DeleteOrder('修改订单失败')

    webPos = 0
    for name in self.rest:
        rest = self.rest[name]
        ws = self.ws[name]
        
        pos  = getCaokongPos(ws.pos, Exchange, symbol, bid, MZ, L)
        if not webPos:
            webPos = pos

        if webPos and pos:
            if webPos['side'] == pos['side']:
                webPos['liang'] += pos['liang']
            else:
                webPos['liang'] -= pos['liang']

        if self.side == 'stop':
            self.DeleteOrder('策略已暂停')
            return tlog(f'{Name} 策略已暂停...', '', 60)
        
        elif 'open' in self.side:

            side = 'BUY' if 'Buy' in self.side else 'SELL'
            newMaxPos = maxPos
            if pos and pos['side'] == side:
                newMaxPos -= pos['liang']

            now = 0
            allLiang = 0
            for orderId in self.orders:
                order = self.orders[orderId]
                if order['side'] == side and order['name'] == name:
                    now += order['liang']
                    newMaxPos -= order['liang']
                allLiang += order['liang']
                
            price = randPrice(side, bid, ask, J)
            keyong = getKeyong(name, ws, side, posGg, pos, self.orders)
            usdt2 = min(keyong * posGg * mult, newMaxPos * price)
            usdt = randUsdt(usdt2)
            if not usdt or keyong < 50:
                tlog(f"{name} 可用余额不足", [], 15)
                continue
            
            liang = usdt / price
            if pos and pos['side'] != side:
                liang += pos['liang']
            liang = N(liang, L)

            if NowTime_ms() < self.pre_order_time:
                return tlog(f'{Name} Cd中', [
                    round((self.pre_order_time-NowTime_ms())/1000, 1), 's', U(allLiang*mid)
                    ], 1)
            
            order_id, msg = rest.PostOrder(symbol, side, price, liang, 'GTX')
            if order_id:
                self.orders[order_id] = {
                    'name': name,
                    'rest': rest,
                    'jiage': price,
                    'liang': liang,
                    'jiazhi': N(liang*price, 2),
                    'side': side,
                }
                #冷却0.2 到 8秒
                self.pre_order_time = NowTime_ms() + randTime()

            else:
                print(name, '可开', U(maxPos*price), '本账户可开', U(newMaxPos*price), '当前挂单量', U(now*price),
                       '可用1', keyong, '可用2', ws.keyong, 'lever2', U(keyong*posGg*mult), 'min', usdt2, '本次挂单', U(price*liang),
                       )
                print(pos)
                a = now*price + price*liang
                if pos:
                    a += pos['jiazhi']
                print('求和', U(a))

        elif 'closeAll' == self.side:
            self.DeleteOrder('策略已暂停')
            for side in ['BUY', 'SELL']:
                pos = GetNewPos(ws.pos, Exchange, symbol, side, bid, MZ, L)
                if pos:
                    """ 一次性"""
                    x = 0.0015
                    jiage = bid - (bid * x) if pos['side'] == 'BUY' else ask + (ask * x)
                    jiage = N(jiage, J)
                    jiage2 = bid  if pos['side'] == 'BUY' else ask 
                    log(pos['side'], U(pos['jiazhi']), '平仓', '当前价', jiage2, '偏移价', jiage, StrBi(x, ))

                    liang = pos['liang']
                    if liang * jiage > 200000:
                        liang = N(200000 / jiage, L)

                    rest.PostOrder(symbol, pos['side2'], jiage, liang, 'IOC', 1)
                    Sleep(1000)
                    upPos(self, rest, ws, set=1)
                else:
                    tlog('无仓可清', pos, 60)

    
    # getCaokongData(self, toSymbol(symbol).replace(CcyBi, ''), data, webPos, 0)
    return
    """ 持仓价值"""
    jiazhi = 0

    getCaokongData(self, toSymbol(symbol).replace(CcyBi, ''), data, pos, maxPos)

    pos_limit = self.pos_limit
    if pos:
        jiazhi = round(pos['liang']*ask, 2)

        pos_limit -= jiazhi
        if self.prePos != pos['liang']:
            tlog('持仓价值', [U(jiazhi, 1)], 3)
            self.prePos = pos['liang']

    if pos_limit > maxPos*bid:
        tlog('最大持仓量限制', [maxPos, U(maxPos*bid, 1)], 2*60)
        pos_limit = maxPos*bid


    if not self.pos_limit:
        return tlog('未设置持仓限制...', '', 60)

    if 'stop' == self.side:
        DeleteOrder(self, symbol)
        return tlog(f'{Name} 策略已暂停...', '', 60)

    elif 'open' in self.side:
        side = 'BUY' if 'Buy' in self.side else 'SELL'
        jiage = bid-J2*2 if side=='BUY' else ask+J2*2       #挂买三 或 卖三
        jiage = N(jiage, J)
        
        # if jiazhi >= self.pos_limit and pos['side'] == side:
        #     DeleteOrder(self, symbol)
        #     tlog('持仓达标了', ['持仓', U(jiazhi, 1), '总可开', U(self.ws.usdt*posGg), '目标', U(self.pos_limit)], 3)
        #     return

        if self.stopOpen:
            return tlog('操控趋势已经完成', ['停机'], 60)

        """ 重置"""
        if self.preSide and self.preSide != side:
            DeleteOrder(self, symbol)

        if self.preJia == ask or self.preJia == bid:
            DeleteOrder(self, symbol)
            return tlog('差点成交了!!!!!', '', 3)

        if not tlog('差点成交了!!!!!', '', 3, xs=0, look=1):
            return 0

        """ 监控价格是否超过阈值"""
        if self.preJia:

            bili = (abs(jiage-self.startJia)) / jiage * 100
            if bili >= self.bili:
                DeleteOrder(self, symbol)
                log('超过阈值！！！！！！！！！！！！！！！ 停机', StrBi(bili/100))
                self.stopOpen = 1
                return

            elif jiage != self.preJia2:
                log('趋势操控中', '--->', StrBi(bili/100))
                self.preJia2 = jiage
        
        liang = self.ws.keyong * posGg * 0.99 / mid
        if pos and pos['side'] != side:
            liang += pos['liang']
        if pos and pos['side'] == side:
            maxPos -= pos['liang']
        liang = N(min(liang, maxPos), L)

        if liang*mid > self.pos_limit:
            liang = N(self.pos_limit / mid, L)



        """ 首次挂单"""
        if not self.preJia:
            # print(self.ws.keyong, posGg, maxPos, U(maxPos*jiage))
            order_id, msg = self.bnb.PostOrder(symbol, side, jiage, liang, 'GTX')
            if order_id:
                self.preJia = jiage
                self.preJia2 = jiage
                self.preLiang = liang
                self.preSide = side
                self.startJia = jiage
                self.orderId.append(order_id)
            
            else:
                time.sleep(1)
                os._exit(0)

        #修改价格
        elif (side == 'BUY' and jiage > self.preJia) or (side == 'SELL' and jiage < self.preJia):
            
            try:
                fh = self.bnb.EditOrder(side, symbol, jiage, self.preLiang, self.orderId[0])
            except:
                os._exit(0)
            if fh:
                self.preJia = jiage
            else:
                DeleteOrder(self, symbol)


    else:

        """ 清理所有仓位"""
        if pos and pos['liang'] * bid > 11:
            
            if 'closeAll' == self.side:
                """ 一次性"""
                # 撤销上次挂单
                DeleteOrder(self, symbol)

                x = 0.0015
                jiage = bid - (bid * x) if pos['side'] == 'BUY' else ask + (ask * x)
                jiage = N(jiage, J)
                jiage2 = bid  if pos['side'] == 'BUY' else ask 

                log(pos['side'], jiazhi, '平仓', '当前价', jiage2, '偏移价', jiage, StrBi(x, ))

                self.bnb.PostOrder(symbol, pos['side2'], jiage, pos['liang'], 'GTC', 1)
                self.preJia = bid

                Sleep(5000)
                upPos(self, set=1)

            else:
                #最大砸盘金额
                maxTaker = 3000

                """ 挂卖一"""
                if jiazhi > maxTaker+100 and bid != self.preJia:

                    liang = N((jiazhi-maxTaker) / bid * 0.99, L)
                    if liang * bid > 100:
                        DeleteOrder(self, symbol)
                        order_id, msg = self.bnb.PostOrder(symbol, pos['side2'], ask if pos['side'] == 'BUY' else bid, liang, 'GTX', 1)
                        if order_id:
                            self.preJia = bid

                duishou = bidQty * bid
                isFuck = duishou < 300
                """ 缓慢砸盘"""
                if NowTime_ms() >= self.SujiTime or isFuck:
                    liang = random.randint(50, 300)
                    duishou = bidQty * bid  if pos['side'] == 'BUY' else askQty * ask

                    """ 每6次吃一次对手盘"""
                    if self.takerCount and self.takerCount % 6 == 0:
                        liang = max(duishou*1.1, random.randint(1000, 1500))
                        liang = min(liang, maxTaker)

                    print(f'--------第 {self.takerCount} 次--------')
                    print('对手', U(duishou), 
                        ' ',
                        '持仓', U(jiazhi), 
                            '-------> 本次砸盘', U(min(jiazhi, liang)),'\n')

                    liang = N(liang / bid, L)
                    liang = min(liang, pos['liang'])

                    DeleteOrder(self, symbol)
                    order_id, msg = self.bnb.PostOrder(symbol, pos['side2'], bid, liang, 'normal', 1)
                    if order_id:
                        self.takerCount += 1
                    
                    waitTime = random.randint(1500, 4800)
                    self.SujiTime = NowTime_ms() + waitTime
                    print('等待', round(waitTime / 1000, 1), 's\n')

                    # Sleep(500)
                    # upPos(self, set=1)


        else:
            if tlog('平仓完毕', pos, 60):
                delPos(self, symbol, 'BUY')
                delPos(self, symbol, 'SELL')
                time.sleep(5)
                upPos(self, set=1)
                