# -*- coding: utf-8 -*-
import os
import time
import sys
import json
import uvloop
import pandas as pd
import traceback        #输出报错
from pprint import pprint
from decimal import Decimal
import threading    #多线程
import base64
import fasthttp

from ws4py.client.threadedclient import WebSocketClient
dirpath = os.path.abspath('.')+'/main'
sys.path.append(dirpath)
print(dirpath)

from main.hanshu import *
from main.config import *
from moneyV2.money import *
from moneyV2.table import *
from super.spot import Spot
from super.swap import Swap
from moneyV2.config_Exchange import *

from main.api import api

class Zero():
    def __init__(self):
        self.pos = []
        self.usdt = 0.0001

    def GetPos(self):
        return []

    def main(self):
        return []

def GetConfig(data=''):

    #重复尝试上传十次，每次间隔1秒
    for x in range(10):
        try:
            post = {'data': ''}
            if data:
                post['data'] = base64.b64encode(json.dumps(data).encode())
            
            fanhui = requests.post('https://rich.wusisanba.com/api/CaoKong/config', data=post, timeout=10)
            fJson = fanhui.json()['data']
            return fJson
        
        except Exception as e:
            try:
                print(fanhui.text)
            except:
                pass
            traceback.print_exc()
        
        time.sleep(1)

    return False

config = GetConfig()
SYMBOl = config['symbol']
TMP_SYMBOL = SYMBOl


# bnb = bian(apiKey='吃了吗资本')

# # bit = bitget(apiKey='5_4')
# emails = """<EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>
# <EMAIL>"""
# emails = emails.split('\n')
# emails2 = []
# for v in emails:
#     if v:
#         emails2.append(v)
#     else:
#         print('重复', v)
# emails = emails2
# print(len(emails))

# # bit.ziHua(10000, 'mix_usdt', 'spot', '5460941098', '8202015437')


# yuer = 0

# all = 0
# kui = 0
# """ 现货转入子账户"""
# for v in emails:
#     spot = bnb.GetZiYuer(v)
#     print(v, spot)
#     all += spot['all']
#     kui += spot['yingkui']
#     # continue
#     cha = Si(yuer - spot['all'], 4)
#     if abs(cha) > 100:
#         if cha > 0:
#             bnb.ziHua(cha, 'SPOT', 'USDT_FUTURE', jie=v)
#         else:
#             bnb.ziHua(min(cha * -1, round(spot['keyong']*0.99)), 'USDT_FUTURE', 'SPOT', fa=v)
# print('总余额', all, '总盈亏', kui)

# input('wait..')


class kuaipao():


    def __init__(self):
        global SYMBOl
        
        trades = [
            ['Bian', '吃了吗资本'],
            ['Bian', 'test19'],
            ['Bian', '火币-球球'],
            ['Bian', 'bybit4'],
            ['Bian', 'coinex23'],
            ['Bian', 'bybit6'],
            ['Bian', 'test16'],
            ['Bian', 'test15'],
            ['Bian', 'bybit7'],
            ['Bian', '火币-球球6'],
            ['Bian', 'bitget3'],
            ['Bian', 'trade16'],
            ['Bian', 'trade161'],
            ['Bian', 'trade17'],
            ['Bian', 'trade162'],
            ['Bian', 'gate2'],
            ['Bian', 'gate3'],
            ['Bian', 'gate4'],
            ['Bian', 'gate5'],
            ['Bian', 'gate6'],
            ['Bian', 'gate7'],
            ['Bian', 'gate8'],
            ['Bian', 'gate9'],
            ['Bian', 'bitget4'],
            ['Bian', 'bitget6'],
            ['Bian', 'test9'],
            ['Bian', 'test6'],
            ['Bian', 'bitget14'],
            ['Bian', 'Coinex-球球'],
            ['Bian', 'coinex4-球球'],
            ['Bian', 'coinex5-球球'],
            ['Bian', 'coinex13'],
            ['Bian', 'ku1'],
            ['Bian', 'ku3'],
            ['Bian', 'ku8'],
            ['Bian', 'test10'],
            ['Bian', 'test12'],
            ['Bian', 'test22'],
            ['Bian', 'bitget2'],
            ['Bian', 'bitget8'],
            ['Bian', 'bitget9'],
            ['Bian', 'bitget10'],
            ['Bian', 'bitget12'],
        ]
        self.rest = {}
        self.ws = {}

        #设置各个账号的Ws和Rest实例
        for trade in trades:
            bnb, ws, self.symbolFun, order_status, self.feilv_time = getExchange(trade[0])
            rest = bnb(apiKey=trade[1], ccyBi=CcyBi)
            self.rest[trade[1]] = rest
            self.ws[trade[1]] = ws(rest, ccyBi=CcyBi, depathCallback=self.depathCallback, orderCallback=self.orderCallback)
            self.ws[trade[1]].pos = rest.GetPos()

            if Name+'_lever' in config:
                self.rest[trade[1]].GangGan = int(config[Name+'_lever'])
        
        self.symbol = self.symbolFun(rest)

        #查找交易对
        for s in self.symbol:
            if SYMBOl+CcyBi == toSymbol(s, noCcyBi=1):
                s2 = toSymbol(s)
                self.look = {f'{s2}': 0}
                SYMBOl = s
                self.symbol = self.symbol[s]

                for rest in self.rest:
                    rest = self.rest[rest]
                    if hasattr(rest, 'SetChicang'):
                        rest.SetChicang(False)

                    # if hasattr(rest, 'DeleteAllOrder'):
                    #     rest.DeleteAllOrder(SYMBOl)
                    
                    # if hasattr(rest, 'GetFeilv'):
                    #     rest.GetFeilv()

                break

        else:
            log('未找到', SYMBOl)
            time.sleep(3)
            os._exit(0)


        self.api = api(0, [], isBnb=0)
        
        #启动Ws
        i = 0
        loop = uvloop.new_event_loop()
        for ws in self.ws:
            ws = self.ws[ws]
            loop.create_task(ws.main())
            if not i:
                self.depth_ws = ws
                loop.create_task(ws.main([SYMBOl]))
                i = 1
        t = threading.Thread(target=start_loop,args=(loop, ))
        t.setDaemon(True)
        t.start()


        self.orders = {}    #本地维护订单


        self.pre_order_time = 0


        self.toSymbolFunc = toSymbol        #money文件里调用不了


        self.data = {}
        self.ganggan = 0
        self.max_ganggan = 0
        self.coinex = Zero()
        self.ws2 = Zero()

        """ 现货"""
        self.startJia = 0      #初始价格
        self.bnb_price = {'price': 0, 'time': 0}
        self.bili = 0      #初始价格
        self.limit_bili = 0      #挂单距离
        self.preJia = 0        #上次挂单价格
        self.preJia2 = 0        #上次记录比例价格
        self.preSide = 0        #上次挂单价格
        self.preLiang = 0        #上次挂单数量
        self.preOrderLiang = 0
        self.preTakerJia = 0   #上次挂单价格
        self.prePos = 0
        self.preTime = 0
        self.orderJia = 0
        self.kejie = 0          #可借币余额
        self.usdt = 0           #现货当前余额
        self.SujiTime = 1       #随机等待时间
        self.takerCount = 0     #第几次吃单
        self.side = 'stop'      #策略进行的操作
        self.order = 'Maker'
        self.stopOpen = 0       #建仓完毕后，此值改为1（禁止建仓），Web修改操作重置为0
        self.pos_limit = 0          #从Web获取持仓金额限制，从而决定挂单量
        self.error = 0
        self.orderId = []
        self.orderIdLiang = {}     #存储变化的量
        self.getMax = ['Bitget', 'Ku', 'Okx']
        self.depthTime = 0      #深度更新时间
        self.depthData = 0      #深度


        self.tables =  {
            "title" : Exchange,
            "cols" : [
                '交易对',
                '买一 | 卖一', '数量',
                'Z', "开仓价", '平仓价'
                '盈亏',
                '开仓时间',
            ],
            "rows" : [],
        }
        self.DeleteOrder(qiangzhi=1)
        
        t = threading.Thread(target=self.upConfig)
        t.setDaemon(True)
        t.start()
        
        t = threading.Thread(target=self.celue)
        t.setDaemon(True)
        t.start()



    """ 订单更新"""
    def orderCallback(self, data):
        try:
            if data['liang'] and data['id'] in self.orders:
                self.DeleteOrder('订单触发回调')

        except Exception as e:
            uploadError(traceback.format_exc())
            os._exit(0)


    """ Ws更新"""
    def depathCallback(self, symbolWs, data, isbnb=0):
        s = toSymbol(symbolWs)

        if self.look[s]:
            return tlog('处理中', [s, '跳过...', Color('', 1)], 60 * 60)

        self.look[s] = 1
        self.depthTime = NowTime_ms()
        self.depthData = data
            
        if Name == 'spot':  
            Spot(self, SYMBOl, data)

        elif Name == 'swap':
            Swap(self, SYMBOl, data)

        else:
            if Exchange in self.getMax:
                jiage = N((data['bidPx']+data['askPx']) * 0.5, self.symbol['J'])
                data['maxLiang'] = N(self.getMaxPos(SYMBOl, jiage=jiage) / jiage, self.symbol['L'])
                if 'M' not in self.symbol.keys():
                    self.symbol['M'] = data['maxLiang']  #最大下单

            Getswap(self, SYMBOl, data)

        self.look[s] = 0

    
    """ 撤销所有订单"""
    def DeleteOrder(self, msg='', qiangzhi=0):
        
        if self.orders or qiangzhi:
            ttt = []
            for rest in self.rest:
                rest = self.rest[rest]
                # ttt.append(MyThread(func=rest.DeleteAllOrder, args=(NowTime_ps(), SYMBOl, msg, )))
                ttt.append(rest.DeleteAllOrder(NowTime_ps(), SYMBOl, msg, httpData=1))
                
            t = NowTime_ms()
            responses = fasthttp.batch_request(ttt)
            log('撤销所有账号订单', NowTime_ms() - t, 'ms', responses[0], msg)
            self.orders = {}


        self.orders = {}


    def upYuer(self, rest, ws):
        """ Ws只有可用余额，就得用rest获取可用余额"""
        if Name == 'spot':
            yuer = rest.GetYuer(p=0, bi=SYMBOl)
        else:
            yuer = rest.GetYuer(p=0)

        if yuer:
            ws.usdt = yuer['all']
            ws.keyong = yuer['keyong']
            ws.yingkui = yuer['yingkui']


    """ 定时更新余额持仓和线程"""
    async def _update_account(self, rest, ws):
        tlog('[线程]', '更新持仓和余额启动...', 10)
        
        self.posTime = 0
        while True:
            try:
                self.upYuer(rest, ws)

                if NowTime_ms() - self.posTime > 5 * 1000:
                    upPos(self, rest, ws)
                
                """ 购买BNB"""
                if tlog(f'{rest.ApiName}查询BNB余额', '', 10, xs=0):
                    if NowTime_ms() - self.bnb_price['time'] > 60 * 1000:
                        self.bnb_price['time'] = NowTime_ms()
                        self.bnb_price['price'] = rest.GetNowJiage('BNBUSDT')
                    
                    jiage = self.bnb_price['price']
                    bnb = float(rest.GetYuer(p=0, bi="BNB")['all'])
                    rest.bnb_jiazhi = jiage * bnb


                    if ws.keyong > 1000 and rest.bnb_jiazhi < 20:
                        usdt = float(rest.GetXianhuo('USDT'))
                        rest.Huazhuan(round(100.1 - usdt, 6))
                        log('买bnb', rest.go("POST", '/api/v3/order', {"symbol": 'BNBUSDT', 'side': "BUY", 'type': "MARKET" , 'quoteOrderQty': 100}, url='https://api.binance.com'))
                        time.sleep(1)

                        #留在现货里的BNB划转回去
                        yuerZiXianhuo = float(rest.GetXianhuo("BNB"))
                        if yuerZiXianhuo > 0:
                            rest.Huazhuan(yuerZiXianhuo, 'MAIN_UMFUTURE', 'BNB') 

                        usdt = float(rest.GetXianhuo("USDT"))
                        if usdt > 0 and usdt < 10:
                            rest.Huazhuan(usdt, 'MAIN_UMFUTURE') 
                
                await asyncio.sleep(1)

            except Exception as e:
                uploadError(traceback.format_exc())
                os._exit(0)


    """ 获取最大最大可开数量"""
    def getMaxPos(self, symbol, jiage=1):

        if self.symbol['maxJiazhi'] == 0:
            if Exchange == 'Bitget' and self.ganggan:
                self.symbol['maxJiazhi'] = self.bnb.GetMaxPos(symbol, jiage, self.ws.usdt, self.ganggan)

            if Exchange == 'Ku':
                self.symbol['maxJiazhi'] = self.bnb.GetMaxPos(symbol)

            if Exchange == 'Okx':
                m, price = self.bnb.GetMaxPos(symbol)
                log('Okx', symbol, '最大可开', m, '面值', self.symbol['Z'], '价值', U(m*self.symbol['Z']*price))
                self.symbol['maxJiazhi'] = m * self.symbol['Z'] * price

        return self.symbol['maxJiazhi']
    

    """ 定时更新余额持仓和线程"""
    def celue(self):
        time.sleep(1)
        log('[线程]', '策略启动...')
        while True:
            data = getDepth(SYMBOl, self.depth_ws.data)

            if data:

                """ 检查策略上次运行时间"""
                if NowTime_ms() - self.depthTime > 100:
                    self.depathCallback(SYMBOl, data)
            
            else:
                tlog('没有深度数据', data, 3)

            Sleep(100)


    """ 定时更新操作"""
    def upConfig(self):
        log('[线程]', '获取设置启动...')
        upData = 1
        preLever = 0
        while True:
            
            try:
                """ 更新WebTable"""
                if self.depthData:
                    J, L, Max, Min, MZ         = self.symbol['J'], self.symbol['L'], self.symbol['M']*0.99, self.symbol['Min'], self.symbol['Z']
                    posGg = self.symbol['GangGan']
                    bid, ask = float(self.depthData['bidPx']), float(self.depthData['askPx'])
                    
                    maxPos = MAX
                    if 'maxJiazhi' in self.symbol and self.symbol['maxJiazhi']:
                        maxPos  = min(maxPos, N(self.symbol['maxJiazhi'] / bid, L))       #最大持仓量
                    
                    if 'maxLiang' in data and data['maxLiang']:
                        maxPos = min(maxPos, data['maxLiang'])

                    self.data = []
                    for name in self.rest:
                        rest = self.rest[name]
                        ws = self.ws[name]
                        
                        gualiang = 0
                        for orderId in self.orders:
                            order = self.orders[orderId]
                            if order['name'] == name:
                                gualiang += order['liang'] * order['jiage']

                        data = {
                            'name': name,
                            'yuer': round(ws.usdt, 1),
                            'keyong': round(ws.keyong, 1),
                            'symbol': SYMBOl.replace(CcyBi, '')+f'[{posGg}x]'+f'[{self.max_ganggan}x]',
                            'side': '-',
                            'liang': '-',
                            'liang_u': 0,
                            'gualiang': round(gualiang),
                            'maxliang': round(maxPos*ask),
                            'Z': MZ,
                            'jiage': 0,
                            'ping_jiage': ask,
                            'yingkui': 0,
                            'yingkui2': 0,
                            'create_time': '-',
                            'time': NowTime(),
                            'okOpen': 1 if self.error >= 2 or self.stopOpen else 0,
                        }
                        pos  = GetPos(SYMBOl, ws.pos)

                        if pos and pos['liang']:
                            ping_jiage = ask if 'SELL' == pos['side'] else bid
                            yingkui, _ = GetRoe(ping_jiage, pos, MZ)
                            jiazhi = pos['liang']*ask*MZ

                            data['side'] = Color('▲多', 1, html=1) if 'BUY' == pos['side'] else Color('▼空', -1, html=1)
                            data['liang'] = STR_N(jiazhi, 0)+'$'+ ' (' + StrBi(jiazhi/ws.usdt, 0)+')'
                            data['liang_u'] = jiazhi
                            data['jiage'] = N(pos['jiage'], J)
                            data['ping_jiage'] = ping_jiage
                            data['yingkui'] = Color(round(yingkui, 1), html=1)+' ('+StrBi(yingkui/jiazhi)+')'
                            data['yingkui2'] = yingkui

                        self.data.append(data)

                data = ''
                if upData:  #每两次传递一次数据
                    data = self.data
                    upData = 0
                else:
                    upData = 1

                data = GetConfig(data)
                if data:

                    if Name not in data:
                        log('未配置', Name, data)
                        time.sleep(5)
                        continue

                    if self.bili != abs(float(data['bili'])):
                        log(Name, '变化最大操控比例', self.bili, '->', abs(float(data['bili'])))
                        self.bili = abs(float(data['bili']))
                        self.stopOpen = 0

                    if self.limit_bili != abs(float(data['limit_bili'])):
                        log(Name, '变化挂单操控比例', self.limit_bili, '->', abs(float(data['limit_bili'])))
                        self.limit_bili = abs(float(data['limit_bili']))
                        self.stopOpen = 0
                    
                    if Name != 'spot' and self.order != data['order']:
                        pass
                        # log(Name, '变化订单', self.order, '->', data['order'])
                        # self.order = data['order']
                        # self.preJia = 0
                        # self.stopOpen = 0
                        # Delete
                        # if hasattr(self.bnb, 'DeleteAllOrder'):
                        #     self.bnb.DeleteAllOrder(SYMBOl)

                    if self.side != data[Name]:
                        log(Name, '变化状态', self.side, '->', data[Name])
                        self.side = data[Name]
                        self.stopOpen = 0
                        self.takerCount = 0
                        self.startJia = 0
                        if self.side == 'rebot':
                            log('收到重启指令')
                            self.side = 'stop'
                            self.DeleteOrder()
                            time.sleep(1)
                            os._exit(0)
                        

                    if TMP_SYMBOL != data['symbol']:
                        log(Name, '交易对变化',  TMP_SYMBOL, '->', data['symbol'])
                        uploadLog(isExit=1)

                    if Name+'_lever' in data:
                        lever = int(data[Name+'_lever'])
                        for name in self.rest:
                            rest = self.rest[name]
                            rest.GangGan = int(config[Name+'_lever'])
                    else:
                        lever = 0
                        tlog('未找到杠杆设置', lever, 3)

                    if Name+'_pos' in data:
                        limit = float(data[Name+'_pos'])
                    else:
                        limit = 0
                        tlog('未找到持仓设置', limit, 3)
                    
                    if lever != preLever:
                        
                        for name in self.rest:
                            if not self.max_ganggan:
                                self.max_ganggan = rest.GetMaxGangGan(SYMBOl)
                            rest = self.rest[name]
                            rest.GangGan = int(config[Name+'_lever'])

                            if Exchange == 'Huobi':
                                rest.symbol = {SYMBOl: {'GangGan': lever}}

                            self.ganggan = lever

                            
                            if hasattr(rest, 'GetMaxGangGan'):
                                dq = min(self.max_ganggan, lever)
                                preDq = SetJson('/root/lever.json', {})
                                
                                if SYMBOl not in preDq:
                                    preDq[SYMBOl] = 0

                                if dq != preDq[SYMBOl]:
                                    fh = rest.SetGangGan(SYMBOl, dq)
                                    preDq[SYMBOl] = dq
                                    SaveJson('/root/lever.json', preDq)

                                self.ganggan = dq
        
                            else:
                                if Exchange == 'Bybit':
                                    rest.SetGangGan(SYMBOl, self.symbol['GangGan'], self.symbol['riskId'])
                                else:
                                    rest.SetGangGan(SYMBOl, lever)

                            rest.GangGan = lever
                            self.symbol = self.symbolFun(rest)[SYMBOl]
                            tlog(f'{SYMBOl} 最大可开价值', U(self.symbol['maxJiazhi']), 10)
                            if Exchange == 'Deepcoin':
                                self.symbol['M'] = fh
                                self.symbol['GangGan'] = dq

                            preLever = lever

                            if Exchange in self.getMax:
                                self.symbol['maxJiazhi'] = 0

                    
                    if limit !=  self.pos_limit:
                        log(Name, '变化持仓限额', U(self.pos_limit), '->', U(limit), Color('', -1))
                        self.pos_limit = limit
                        self.stopOpen = 0

            except Exception as e:
                uploadError(traceback.format_exc())
                os._exit(0)

            Sleep(600)


    def main(self):
        self.loop = uvloop.new_event_loop()
        for name in self.rest:
            rest = self.rest[name]
            ws = self.ws[name]
            self.loop.create_task(self._update_account(rest, ws))
        self.loop.run_forever()




def runMain():
    bnb = bian(apiKey='吃了吗资本')

    emails = bnb.GetZiList()
    """ 保存到CSV"""
    with open('子账户.csv', 'w') as f:
        f.write('email,address,network\n')
        for email in emails:
            f.write(f'{email},{bnb.GetZiAddress(email, "BSC")}\n')
            f.write(f'{email},{bnb.GetZiAddress(email, "TRX")}\n')

    print('保存完毕')
    input('')

    # for i in range(19,51):
    #     name = f'pan{i}'
    #     bnb.CreateNumber(name)
    #     time.sleep(0.2)

    # input('')



    try:
        m = kuaipao()
        while 1:
            m.main()

    except Exception as e:
        uploadError(traceback.format_exc())

if __name__=='__main__':
    runMain()