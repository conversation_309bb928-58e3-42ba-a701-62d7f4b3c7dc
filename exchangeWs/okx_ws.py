#!/usr/bin/python3
# *_* coding= utf-8 *_*
import aiohttp
import asyncio
import json
import ujson
import hmac
import time
import traceback
from main.config import *
from main.hanshu import *

from moneyV2.money import changePos

class okx_ws():
    def __init__(self, coinex, depathCallback=0, ccyBi=0):
        self.coinex = coinex
        self.access_id = coinex.access_id
        self.secret_key = coinex.secret_key
        self.passwd = coinex.passwd
        self.depathCallback = depathCallback
        self.symbol = coinex.symbol

        all = self.coinex.GetYuer(p=0)
        self.usdt = all['all']
        self.yingkui = all['yingkui']
        self.keyong = all['keyong']
        self.preUsdt = 0.01

        self.data = {}
        self.orders = {}
        self.depth = {}
        self.conn = 0


    """ 更新订单"""
    def _update_order(self, data2):
        try:
            if type(data2) != dict:
                return False

            data2['px'] = data2['px'] if data2['px'] else -1
            data2['avgPx'] = float(data2['avgPx']) if data2['avgPx'] else 0


            symbol = data2['instId']
            self.orders[data2['ordId']] = {
                'symbol': symbol,              #交易对
                'status': data2['state'],               #状态
                'side': data2['side'].upper(),          #方向
                'type': data2['ordType'],               #订单类型
                'jiage': data2['avgPx'],              #累积成交均价
                'liang': float(data2['accFillSz']),   #累积成交量
                'jiazhi': 0,         #委托价值
                'sxf': float(data2['fee'])*-1,             #手续费
                'fsxf': float(data2['rebate']),         #返佣手续费
                'yingkui': float(data2['pnl']),         #盈亏
                'time': NowTime(),                 #推送时间
            }
            if symbol in self.symbol:
                MZ = self.symbol[symbol]['Z']
            else:
                MZ = 1

            liang = float(data2['sz'])
            liang2 = float(data2['accFillSz'])
            jiage = float(data2['px']) if float(data2['px']) > 0 else float(data2['avgPx'])
            jiage2 = float(data2['avgPx'])

            jiazhi = jiage *  liang * MZ if liang else 0.00001
            jiazhi2 = jiage2 * liang2 * MZ if liang2 else 0.00001


            msg = [
                '[OKX监听订单]', GetTime(echoMs=1), '', data2['side'].upper(), symbol, data2['state'],
                "　ID", data2['ordId'], data2['ordType'],
                '　价格', jiage, '数量', data2['sz']+ U(jiazhi, kuo=1),
                '　成交价', jiage2, '成交量', data2['accFillSz']+U(jiazhi2, kuo=1),
                '　盈亏', float(data2['pnl']),
                '　手续费', str(data2['fee'])+StrBi(float(data2['fee']) / jiazhi2, 3, True),
                '　面值', MZ,
            ]

     
            if liang2:
                log(*msg)

                if data2['state'] in ['filled', 'canceled']:
                    changePos(self, symbol, data2['side'].upper(), jiage2, liang2)

            else:
                print(*msg)


            if len(self.orders) > 500:
                self.orders = dict(list(self.orders.items())[-100:])


        except Exception as e:
            uploadError(traceback.format_exc())


    """ 更新余额"""
    def _update_account(self, data2):
        
        if data2['availEq'] == '':
            return log("合约未开通")

        self.usdt = N(float(data2['cashBal']), 4) + N(float(data2['upl']), 4)
        self.yingkui = N(float(data2['upl']), 4)
        self.keyong = N(float(data2['availEq']), 4)

        if self.usdt != self.preUsdt:
            print('[OKX余额更新]', self.usdt)

        self.usdt = max(self.usdt, 0.01)
        self.preUsdt = self.usdt
        

    """ 更新资金费率"""
    def _update_feilv(self, data):
        s = data['instId']
        if s not in self.data:
            self.data[s] = {}

        self.data[s]['feilv'] = round(float(data['fundingRate'])*100, 4)


    """ 更新盘口信息"""
    def _update_depth(self, data):
        s = data['arg']['instId']

        if s not in self.data:
            self.data[s] = {'feilv': 0}
        
        v = data['data'][0]
        self.data[s]['bidPx']  = float(v['bids'][0][0])
        self.data[s]['askPx']  = float(v['asks'][0][0])
        self.data[s]['bidQty'] = float(v['bids'][0][1])
        self.data[s]['askQty'] = float(v['asks'][0][1])
        self.data[s]['t']      = int(v['ts'])


    async def ping(self, conn):
        while True:
            await conn.send_str('ping')
            await asyncio.sleep(10)


    async def auth(self, conn):
        timestamp = str(int(time.time()))
        message = timestamp + 'GET' + '/users/self/verify'

        mac = hmac.new(bytes(self.secret_key, encoding='utf8'), bytes(message, encoding='utf-8'), digestmod='sha256')
        d = mac.digest()
        sign = base64.b64encode(d)

        login_param = {"op": "login", "args": [{"apiKey": self.access_id,
                                                "passphrase": self.passwd,
                                                "timestamp": timestamp,
                                                "sign": sign.decode("utf-8")}]}
        login_str = ujson.dumps(login_param)
        await conn.send_str(login_str)

        res = await conn.receive(timeout=30)
        log('okx ws 登录', json.loads(res.data)) 



    async def main(self, symbols=[]):
        while True:
            log("OKX 准备连接ws", symbols)
            try:
                
                try:
                    url = 'ws://10.2.0.200/login' if not symbols else 'ws://10.2.0.200/symbol'
                    print('Okx加速线路', url, OkxCelo)
                except:
                    url = 'wss://ws.okex.com:8443/ws/v5/private' if not symbols else 'wss://ws.okex.com:8443/ws/v5/public'

                    
                async with aiohttp.ClientSession(
                        connector = aiohttp.TCPConnector(
                                limit=50,
                                keepalive_timeout=120,
                                verify_ssl=False,
                                # local_addr=(self.ip,0)
                            )
                        ).ws_connect(
                            url,
                            proxy=None,
                            timeout=30,
                            # receive_timeout=30,
                ) as conn:


                    if symbols:
                        channels = []
                        for symbol in symbols:
                            channels.append({"channel": "funding-rate", "instId": symbol})
                            channels.append({"channel": "bbo-tbt", "instId": symbol})

                    else:

                        # auth
                        await self.auth(conn)

                        self.conn = conn
                            
                        # 订单频道
                        channels = [
                            {"channel": "orders", "instType": "SWAP"},
                            # {"channel": "positions", "instType": "SWAP"},
                            {"channel": "account", "ccy": "USDT"},
                        ]


                    sub_param = {"op": "subscribe", "args": channels}
                    sub_str = json.dumps(sub_param)

                    await conn.send_str(sub_str)
                    
                    asyncio.create_task(self.ping(conn))

                    # loop to process update data
                    while True:
                        
                        # 接受消息
                        try:
                            msg = await conn.receive(timeout=30) #
                        except asyncio.CancelledError:
                            log('ws取消')
                            return
                        except asyncio.TimeoutError:
                            log(f'okx ws长时间没有收到消息 准备重连...')
                            time.sleep(3)
                            break
                        except:
                            print(traceback.format_exc())
                            log(f'okx ws出现错误 准备重连...')
                            time.sleep(3)
                            break
                    

                        msg = msg.data
                        
                        try:
                            if 'pong' in msg:
                                continue
                        except:
                            pass

                        try:
                            msg = ujson.loads(msg)
                        except:
                            log('OKXws解析失败', msg)
                            time.sleep(3)
                            break
                        

                        if type(msg) == dict and "data" in msg.keys():
                            for data2 in msg['data']:
                                if 'arg' not in msg:
                                    print(msg)
                                
                                elif msg['arg']['channel'] == 'orders':
                                    """订单信息"""
                                    self._update_order(data2)

                                elif msg['arg']['channel'] == 'account':
                                    """账户余额信息"""
                                    
                                    try:
                                        data2['details'][0]
                                    except:
                                        continue
                                    
                                    self._update_account(data2['details'][0])

                                elif msg['arg']['channel'] == 'funding-rate':
                                    """资金费率信息"""
                                    self._update_feilv(data2)

                                elif msg['arg']['channel'] == 'bbo-tbt':
                                    """资金费率信息"""
                                    self._update_depth(msg)
                                
                                else:
                                    print(msg)

                                # """持仓信息"""
                                # if res['arg']['channel'] == 'positions':
                                #     self._update_pos(data2)

            except Exception as e:
                print(traceback.format_exc())
                log(f'OKX ws连接失败 开始重连...')
                time.sleep(3)


if __name__ == "__main__":
    # logging.getLogger("asyncio").setLevel(logging.DEBUG)
    loop = asyncio.get_event_loop()
    asyncio.run(main())
    # loop.set_debug(enabled=True)
    print("websocket demo")