#!/usr/bin/python3
# *_* coding= utf-8 *_*
from sys import path
from typing import Dict
import aiohttp
import asyncio
import json
import ujson
import hmac
import time
import traceback
import os

if __name__ == "__main__":
    path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
    
from exchangeV2 import bian
from main.config import *
from main.hanshu import *

class bian_ws():
    def __init__(self, bnb, depathCallback=0, spot=0, ccyBi=0, orderCallback=0):
        self.data = {}
        self.orders = {}
        self.bnb = bnb
        self.spot = spot
        self.expired_time = 300
        self.depathCallback = depathCallback
        self.orderCallback = orderCallback
        self.ccyBi = ccyBi if ccyBi else CcyBi

        all = self.bnb.GetYuer(p=0)
        self.usdt = all['all']
        self.yingkui = all['yingkui']
        self.keyong = all['keyong']
        self.update_time = NowTime()

    """ 查询订单信息，备用Res"""
    # def getOrder(self, orderId, symbol=0, data=0):
    #     status = False
    #     order_liang = 0
    #     order_jia = 0

    #     if orderId in self.orders.keys():
    #         if data:
    #             return self.orders[orderId]

    #         status = self.orders[orderId]['status']
    #         order_liang = self.orders[orderId]['liang']
    #         order_jia = self.orders[orderId]['jiage']

    #     else:
    #         order = self.bnb.GetOrder(orderId, symbol)
    #         if data:
    #             return order

    #         if order:
    #             status = order['status']
    #             order_liang = order['liang']
    #             order_jia = order['jiage']
    #             self.orders[orderId] = order
    #         else:
    #             status = 'False'

    #     return status, order_liang, order_jia

    """ 更新费率"""

    def _update_feilv(self, msg):
        msg = ujson.loads(msg)['data']
        s = msg['s']
        if s not in self.data:
            self.data[s] = {}
        self.data[s]['feilv'] = float(msg['r']) * 100

    """ 更新盘口信息"""

    def _update_tick(self, msg):
        msg = ujson.loads(msg)
        msg = msg['data']
        s = msg['s']
        x = X(s)
        if s not in self.data:
            self.data[s] = {
                'feilv': self.bnb.GetSymbolFeilv(s) if self.bnb else 0}

        self.data[s]['bidPx'] = float(msg['b']) / x
        self.data[s]['bidQty'] = float(msg['B']) * x
        self.data[s]['askPx'] = float(msg['a']) / x
        self.data[s]['askQty'] = float(msg['A']) * x
        self.data[s]['t'] = msg['E']
        # self.data[s]['t2'] = NowTime_ms() - msg['E']

        if self.depathCallback:
            self.depathCallback(s, self.data[s], 'BianSwap')

        self.update_time = NowTime()

    """ 更新盘口信息"""

    def _update_depth(self, msg):
        msg = ujson.loads(msg)
        msg = msg['data']
        s = msg['s']
        x = X(s)
        if s not in self.data:
            self.data[s] = {
                'feilv': self.bnb.GetSymbolFeilv(s) if self.bnb else 0}

        self.data[s]['bidPx'] = float(msg['b'][0][0]) / x
        self.data[s]['bidQty'] = float(msg['b'][0][1]) * x
        self.data[s]['askPx'] = float(msg['a'][0][0]) / x
        self.data[s]['askQty'] = float(msg['a'][0][1]) * x
        self.data[s]['t'] = msg['E']
        self.data[s]['depth'] = 1
        # self.data[s]['t2'] = NowTime_ms() - msg['E']

        if self.depathCallback:
            self.depathCallback(s, self.data[s], 'BianSwap')

        self.update_time = NowTime()

    """ 更新订单"""

    def _update_order(self, msg):
        try:

            msg = ujson.loads(msg)
            data2 = msg['o']
            if data2['X'] == 'EXPIRED' and data2['i'] in self.orders:
                data2['L'] = self.orders[data2['i']]['jiage']

            symbol = data2['s']
            x = X(symbol)
            data2['rp'] = float(data2['rp'])
            data2['n'] = float(data2['n']) if 'n' in data2 else 0
            jiage = float(data2['L']) if float(
                data2['L']) else float(data2['p'])
            self.orders[data2['i']] = {
                'id': data2['i'],  # 交易对
                'symbol': symbol,  # 交易对
                'status': data2['X'],  # 状态
                'jiage': jiage / x,  # 价格
                'liang': float(data2['z']) * x,  # 数量
                'side': data2['S'],  # 方向
                'yingkui': float(data2['rp']),  # 盈亏
                'time': msg['E'],  # 推送时间
            }

            name = '开空' if data2['S'] == 'SELL' else '开多'
            if data2['R']:
                name = '平多' if data2['S'] == 'SELL' else '平空'

            jiazhi = float(data2['p'])*float(data2['q'])
            jiazhi2 = float(data2['z'])*jiage
            if symbol[-4:] == self.ccyBi:
                msg = [GetTime(echoMs=1), '[币安订单]', symbol, name, data2['o'], data2['f'], "　ID", data2['i'], data2['X'], '　方向', data2['S'],
                       '　价格', data2['p'], '　数量', data2['q']+U(jiazhi, kuo=1),
                       '　成交价格', jiage, '　成交数量', str(data2['z'])+U(jiazhi2, kuo=1), '　盈亏', data2['rp'], '　手续费', data2['n']]

                if jiazhi2:
                    del msg[0]
                    log(*msg)

                    """ 更新可用金额，Rest太慢"""
                    if not data2['R'] and data2['X'] in ['FILLED', 'EXPIRED']:
                        self.keyong -= jiazhi2/SetGangGan*1.1

                    # if data2['X'] in self.order_status:
                    #     changePos(self, symbol, data2['S'], float(data2['L']) / x, float(data2['z']) * x, data2['R'])

                else:
                    print(*msg)

            if self.orderCallback:
                self.orderCallback(self.orders[data2['i']])

            if len(self.orders) > 500:
                self.orders = dict(list(self.orders.items())[-100:])

        except Exception as e:
            uploadError(traceback.format_exc())

    """ 更新余额"""

    def _update_account(self, msg):
        msg = ujson.loads(msg)
        for v in msg['a']['B']:
            if v['a'] == self.ccyBi:
                print("[币安余额更新]", v['wb'])

        # 不兼容
        for v in msg['a']['P']:
            x = X(v['s'])
            symbol = v['s']
            side = 'BUY' if float(v['pa']) > 0 else 'SELL'

            ok = {}
            ok['symbol'] = symbol
            ok['liang'] = abs(float(v['pa'])) * x
            ok['side'] = side  # 持仓方向
            ok['side2'] = fanSide(ok['side'])
            ok['jiage'] = float(v['ep']) / x
            ok['yingkui'] = float(v['up'])
            ok['bzj'] = float(v['iw'])

            for i in range(0, len(self.pos)):
                if self.pos[i]['symbol'] == symbol and self.pos[i]['side'] == side:
                    if ok['liang']:
                        self.pos[i] = ok
                    else:
                        del self.pos[i]
                    # print(symbol, '更新仓位', ok)
                    break
            else:
                if ok['liang']:
                    # print(symbol, '新仓位', ok)
                    self.pos.append(ok)

        #     for i in range(0, len(self.pos)):
        #         """ 存在仓位"""
        #         if self.pos[i]['symbol'] == v['s'] and self.pos[i]['side'] == side:
        #             self.pos[i]['jiage'] = float(v['ep']) / x
        #             self.pos[i]['liang'] = abs(float(v['pa'])) * x
        #             self.pos[i]['yingkui'] = float(v['up'])
        #             self.pos[i]['bzj'] = float(v['iw'])
        #             print('修改仓位', v['s'], side, self.pos[i])
        #             break

    async def ping(self, conn, listen_key=0):
        try:
            while 1:
                # if listen_key:
                #     msg = {
                #         "method": "REQUEST",
                #         "params":
                #         [
                #             f"{listen_key}@account"
                #         ],
                #         "id": 12
                #     }
                #     await conn.send_str(ujson.dumps(msg))
                conn.pong()

                await asyncio.sleep(28)
        except:
            print('发送ping失败')

    async def main(self, symbols=[]):
        while True:
            # log("币安 准备连接ws", symbols)
            try:
                url = 'wss://stream.binance.com:9443' if self.spot else 'wss://fstream.binance.com'
                # if 'mad' in ServerName:
                #     url = "wss://fstream-h.binance.com"

                urls = ''
                listen_key = 0
                if symbols:
                    for symbol in symbols:
                        urls += symbol.lower()+"@bookTicker/"
                        urls += symbol.lower()+"@markPrice/"
                        urls += symbol.lower()+"@depth5@100ms/"
                    urls = urls.strip('/')

                    url += "/stream?streams="+urls

                else:
                    listen_key = self.bnb.GetlistenKey()
                    listenKeyTime = time.time()
                    url += f"/ws/{listen_key}"
                    log('[币安Ws] 连接...', url)

                async with aiohttp.ClientSession(
                        connector=aiohttp.TCPConnector(
                            limit=50,
                            keepalive_timeout=120,
                            ssl=False,
                            # local_addr=(self.ip,0)
                        )
                ).ws_connect(
                    url,
                    proxy=None,
                    timeout=30,
                    # receive_timeout=30,
                ) as conn:

                    # note: important to keepalive

                    # asyncio.create_task(self.ping(conn, listen_key))

                    # loop to process update data
                    if listen_key:
                        time_out = 600
                    else:
                        time_out = 30
                    while True:

                        # 接受消息
                        try:
                            msg = await conn.receive(timeout=time_out)
                        except asyncio.CancelledError:
                            log('ws取消')
                            return
                        except asyncio.TimeoutError:
                            log(f'币安 ws长时间没有收到消息 准备重连... {symbols}')
                            break
                        except:
                            print(traceback.format_exc())
                            log(f'币安 ws出现错误 准备重连...')
                            time.sleep(3)
                            break

                        msg = msg.data

                        if not msg:
                            log(f'币安 ws收到空白消息 准备重连...')
                            break

                        if type(msg) == int:
                            log(f'币安 ws收到int消息 准备重连... {msg}')
                            break

                        if 'ping' in msg:
                            await conn.pong()
                            continue

                        if 'listenKeyExpired' in msg:
                            raise Exception('币安Wskey过期重连')
                        elif 'bookTicker' in msg:
                            self._update_tick(msg)
                        elif 'depthUpdate' in msg:
                            self._update_depth(msg)
                        elif 'ORDER_TRADE_UPDATE' in msg:
                            self._update_order(msg)
                        elif 'ACCOUNT_UPDATE' in msg:
                            self._update_account(msg)
                        elif 'markPriceUpdate' in msg:
                            self._update_feilv(msg)

                        if not symbols:
                            if time.time() - listenKeyTime > 60*15:  # 每15分钟续一次
                                log('[币安Ws] 续期listenKey',
                                    self.bnb.PutlistenKey())
                                listenKeyTime = time.time()

                        if symbols and NowTime() - self.update_time > 600:
                            log(f'{symbol} 币安行情超过1分钟未接受到 重连...')
                            break

            except Exception as e:
                log(traceback.format_exc())
                log(f'币安 ws连接失败 开始重连...', url)
                # await asyncio.sleep(3)


if __name__ == "__main__":
    def callback(symbol: str, depth: Dict, msg: str):
        
        """模拟回调"""
        print(f"{symbol}-callback")
        pass


    def callback1(depth: str, msg: Dict):
        """模拟回调"""
        print(f"callback-2")
        pass

    # logging.getLogger("asyncio").setLevel(logging.DEBUG)
    loop = asyncio.get_event_loop()
    bian_ws = bian_ws(bian.bian())
    asyncio.run(bian_ws.main([]))
    # loop.set_debug(enabled=True)
    print("websocket demo")
