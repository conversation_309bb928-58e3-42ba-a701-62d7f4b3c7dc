#!/usr/bin/python3
# *_* coding= utf-8 *_*
import aiohttp
import asyncio
import json
import os
import ujson
import hmac
import time
import hashlib
import traceback

from main.config import *
from main.hanshu import *
from moneyV2.money import changePos


def sign(message, secret_key):
    mac = hmac.new(bytes(secret_key, encoding='utf8'), bytes(message, encoding='utf-8'), digestmod='sha256')
    d = mac.digest()
    return str(base64.b64encode(d), 'utf8')

def pre_hash(timestamp, method, request_path):
    return str(timestamp) + str.upper(method) + str(request_path)


class coinex_spot_ws():
    def __init__(self, gate, depathCallback=0, ccyBi=0):
        self.depathCallback = depathCallback
        self.gate = gate
        self.access_id = gate.access_id
        self.secret_key = gate.secret_key
        
        self.URL = 'wss://socket.coinex.com/'

        self.usdt = 0.01
        self.yingkui = 0.01
        self.keyong = 0.01
        self.preUsdt = 0.01

        self.orders = {}
        self.data = {}
        self.pos = []

        # 过期检查
        self.public_update_time = time.time()
        self.private_update_time = time.time()

        self.id = 1


    """ 更新订单"""
    def _update_order(self, data):
        # pprint(data)
        
        try:
            status = data[0]
            if status == 1:
                status = 'Put'
            elif status == 2:
                status = 'Update'
            elif status == 3:
                status = 'Finish'
            

            data = data[1]

            symbol = data['market']
            side = 'SELL' if data['side'] == 1 else 'BUY'
            orderType = data['type']
            jiazhi = float(data['deal_stock'])
            liang = float(D(data["amount"]) - D(data["left"]))
            if orderType == 1:
                orderType = 'Limit'
            if orderType == 2:
                orderType = 'Marker'

            self.orders[data['id']] = {
                'symbol': symbol,                #交易对
                'status': 'ok',            #状态
                'jiage': float(data['last_deal_price']),      #价格
                'liang': liang,      #数量
                'side': side,              #方向
                'jiazhi': jiazhi,  #价值
                'yingkui': 0,          #盈亏
                'time': NowTime(),              #推送时间
            }

            msg = ['[Coinex现货监听订单]', GetTime(echoMs=1), '', side, symbol, status, "　ID", data['id'], orderType,
            '　价格', data['price'], '数量', data['amount'],
            '　成交价', float(data['last_deal_price']), '成交量', liang,
            '　费率',  str(data['maker_fee'])+'/'+str(data['taker_fee']),
            ]

            if float(data['last_deal_price']):
                log(*msg)
                changePos(self, symbol, side, data['last_deal_price'], liang)
            else:
                print(*msg)

            if len(self.orders) > 500:
                self.orders = dict(list(self.orders.items())[-100:])

        except Exception as e:
            uploadError(traceback.format_exc())



    """ 更新余额"""
    def _update_account(self, data):
        
        for v2 in data:
            if type(v2) != dict:
                continue

            for s in v2:
                v = v2[s]

                if s == CcyBi2:
                    # self.usdt = N(float(i['available']), 4)   #2-8，Rest来统计所有余额，ws更新可用
                    self.keyong = N(float(v['available']), 4)
                    self.yingkui = 0

                    # self.usdt = self.usdt if self.usdt else 0.01
                    self.keyong = self.keyong if self.keyong else 0.01

                    if self.keyong != self.preUsdt:
                        print('[Coinex现货余额更新]', self.keyong)

                self.preUsdt = self.keyong
        
        # pprint(self.pos)
        


    """ 更新盘口和费率信息"""
    def _update_depath(self, msg):
        s, v = msg[-1], msg[-2]
        data= {
            'bidPx': float(v['bids'][0][0]),
            'askPx': float(v['asks'][0][0]),
            'bidQty': float(v['bids'][0][1]),
            'askQty': float(v['asks'][0][1]),
            'maxLiang': MAX,
            'feilv': 0,
            't': NowTime_ms()
        }
        if self.depathCallback:
            self.depathCallback(s, data)

        self.data[s] = data


    async def ping(self, conn):
        sub_str = ujson.dumps({"id": 1, "method": "server.ping","params": []})
        while True:
            await conn.send_str(sub_str)
            await asyncio.sleep(5)


    async def main(self, symbols=[]):
            
        while True:
            try:

                async with aiohttp.ClientSession(
                        connector = aiohttp.TCPConnector(
                                limit=50,
                                keepalive_timeout=120,
                                verify_ssl=False,
                                # local_addr=(self.ip,0)
                            )
                        ).ws_connect(
                            self.URL,
                            proxy=None,
                            timeout=30,
                            receive_timeout=30,
                ) as conn:

                    # 坑  爬深度不能用心跳
                    if symbols:
                        # 订阅深度
                        for symbol in symbols:
                            sub_str = ujson.dumps({"id": self.id, "method": "depth.subscribe", "params": [symbol, 1, "0.000000001", False]})
                            self.id += 1
                            await conn.send_str(sub_str)

                    else:
                        
                        current_time = int(time.time()*1000)
                        sign_str = f"access_id={self.access_id}&tonce={current_time}&secret_key={self.secret_key}"
                        md5 = hashlib.md5(sign_str.encode())
                        param = {
                            "id": 1,
                            "method": "server.sign",
                            "params": [self.access_id, md5.hexdigest().upper(), current_time]
                        }
                        await conn.send_str(ujson.dumps(param))   
                        res = await conn.receive(timeout=30)
                        log('CoinexSpot 订阅', res.data)

                        # 订阅资产
                        sub_str = ujson.dumps({"id": 1, "method": "asset.subscribe","params": [CcyBi2]})
                        await conn.send_str(sub_str)

                        # 订阅订单
                        sub_str = ujson.dumps({"id": 1, "method": "order.subscribe","params": []})
                        await conn.send_str(sub_str)

                    asyncio.create_task(self.ping(conn))


                    while True:
                        
                        # 接受消息
                        try:
                            msg = await conn.receive(timeout=30) #
                        except asyncio.TimeoutError:
                            log(Color('CoinexSpot ws长时间没有收到消息 准备重连...', -1))
                            break
                        except:
                            print(traceback.format_exc())
                            log(Color('CoinexSpot ws出现错误 准备重连...', -1))
                            time.sleep(3)
                            break
                        
                        msg = msg.data
                        if 'pong' in msg:
                            continue

                        try:
                            msg = ujson.loads(msg)
                        except:
                            print('解析失败: '+str(msg))
                            break
                        
                        if 'method' in msg:
                            
                            if 'depth.update' == msg['method']: self._update_depath(msg['params'])
                            elif 'asset.update' == msg['method']: self._update_account(msg['params'])
                            elif 'order.update' == msg['method']: self._update_order(msg['params'])
                            else: pprint(msg)
    

            except Exception as e:
                # uploadError(traceback.format_exc())
                log(f'CoinexSpot ws连接失败 开始重连...', symbols, traceback.format_exc())

                time.sleep(1)
                #强制重启
                # os._exit(0)


if __name__ == "__main__":
    # logging.getLogger("asyncio").setLevel(logging.DEBUG)
    loop = asyncio.get_event_loop()
    asyncio.run(main())
    # loop.set_debug(enabled=True)
    print("websocket demo")