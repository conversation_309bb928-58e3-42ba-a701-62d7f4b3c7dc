from main.config import *
from main.hanshu import *
from os import times
import aiohttp
import asyncio
import ujson
import time
import hashlib
import traceback
from datetime import datetime
from urllib import parse
import hmac
import gzip

class huobi_spot_ws():
    def __init__(self, gate, depathCallback=0, ccyBi=0):
        self.depathCallback = depathCallback
        self.gate = gate
        self.access_id = gate.access_id
        self.secret_key = gate.secret_key
        
        self.URL = 'wss://api-aws.huobi.pro/ws'

        self.usdt = 0.01
        self.yingkui = 0.01
        self.keyong = 0.01
        self.preUsdt = 0.01

        self.orders = {}
        self.data = {}
        self.pos = []

        # 过期检查
        self.public_update_time = time.time()
        self.private_update_time = time.time()

        self.id = 1

    def _update_order(self, data):
        """更新订单"""
        try:
            data = data['data']
            if data['type'][-3] == 'ioc':
                if data['eventType'] in ['creation', 'trade', 'cancellation']:
                    if data['eventType'] == 'creation':
                        eventType = data['eventType']
                        pass
                    elif data['eventType'] == 'trade':
                        eventType = data['eventType']
                        symbol = data['symbol']
                        side = data['type'].split('-')[0]
                        orderType = 'Limit'
                        jiazhi = float(data['orderValue'])
                        liang = float(data["execAmt"]) - float(data["remainAmt"])
                    elif data['eventType'] == 'cancellation':
                        eventType = data['eventType']
                        pass

                self.orders[data['id']] = {
                    'symbol': symbol,                #交易对
                    'status': 'ok',            #状态
                    'jiage': float(data['last_deal_price']),      #价格
                    'liang': liang,      #数量
                    'side': side,              #方向
                    'jiazhi': jiazhi,  #价值
                    'yingkui': 0,          #盈亏
                    'time': NowTime(),              #推送时间
                }

                msg = ['[Huobi现货监听订单]', GetTime(echoMs=1), '', side, symbol, eventType, "　ID", data['id'], orderType,
                '　价格', data['price'], '数量', data['amount'],
                '　成交价', float(data['tradePrice']), '成交量', liang,
                # '　费率',  str(data['maker_fee'])+'/'+str(data['taker_fee']),
                ]

            if float(data['last_deal_price']):
                log(*msg)
            else:
                print(*msg)

            
            if len(self.orders) > 500:
                self.orders = dict(list(self.orders.items())[-100:])

        except Exception as e:
            uploadError(traceback.format_exc())
    
    def _update_account(self, data):
        """更新余额"""
        for v2 in data:
            if type(v2) != dict:
                continue

            if v2['data']['currency'] == CcyBi2:
                # self.usdt = N(float(i['available']), 4)   #2-8，Rest来统计所有余额，ws更新可用
                self.keyong = N(float(v2['data']['available']), 4)
                self.yingkui = 0

                # self.usdt = self.usdt if self.usdt else 0.01
                self.keyong = self.keyong if self.keyong else 0.01

                if self.keyong != self.preUsdt:
                    print('[Huobi现货余额更新]', self.keyong)

            self.preUsdt = self.keyong
        
        """ 更新盘口和费率信息"""
    def _update_depath(self, msg):
        s = msg["ch"].split('.')[1]
        v = msg["tick"]
        data= {
            'bidPx': float(v['bids'][0][0]),
            'askPx': float(v['asks'][0][0]),
            'bidQty': float(v['bids'][0][1]),
            'askQty': float(v['asks'][0][1]),
            'maxLiang': 99999999999,
            'feilv': 0,
            't': NowTime_ms()
        }
        if self.depathCallback:
            self.depathCallback(s, data)

        self.data[s] = data


    async def ping(self, conn):
        sub_str = ujson.dumps({"ping": int(time.time()*1000)})
        while True:
            await conn.send_str(sub_str)
            await asyncio.sleep(5)


    async def main(self, symbols=[], auth=0):
            
        while True:
            try:
                if auth:
                    host = "api-aws.huobi.pro"   #这个最快 2ms
                    method = 'get'
                    path = "/ws/v2"
                    url = 'wss://{}{}'.format(host, path)
                else:
                    url = self.URL

                async with aiohttp.ClientSession(
                        connector = aiohttp.TCPConnector(
                                limit=50,
                                keepalive_timeout=120,
                                verify_ssl=False,
                                # local_addr=(self.ip,0)
                            )
                        ).ws_connect(
                            url,
                            proxy=None,
                            timeout=30,
                            receive_timeout=30,
                ) as conn:
                    # 坑  爬深度不能用心跳
                    if auth:
                        timestamp = datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S")
                        suffix = 'accessKey={}&signatureMethod=HmacSHA256&signatureVersion={}&timestamp={}'.format(
                            self.access_id, parse.quote("2.1"), parse.quote(timestamp))
                        payload = '{}\n{}\n{}\n{}'.format(method.upper(), host, path, suffix)
                        digest = hmac.new(self.secret_key.encode('utf8'), payload.encode(
                            'utf8'), digestmod=hashlib.sha256).digest()
                        signature = base64.b64encode(digest).decode()

                        data = {
                            "action": "req",
                            "ch": "auth",
                            "params":{
                                "authType": "api",
                                "accessKey": self.access_id,
                                "signatureMethod": "HmacSHA256",
                                "signatureVersion": "2.1",
                                "timestamp": timestamp,
                                "signature": signature
                            }
                        }
                        data = ujson.dumps(data)
                        await conn.send_str(data)
                        msg = await conn.receive(timeout=30)
                        if symbols:
                            # 订阅深度
                            for symbol in symbols:
                                # 订阅订单
                                sub_str = ujson.dumps({"action": "sub", "ch": f"orders#{symbol}"})
                                # sub_str = ujson.dumps({"action": "sub", "ch": f"trade.clearing#{symbol}#1"})
                                await conn.send_str(sub_str)
                        else:
                            # 订阅资产
                            sub_str = ujson.dumps({"action": "sub", "ch": "accounts.update"})
                            await conn.send_str(sub_str)       
                    else:
                        if symbols:
                            # 订阅深度
                            type = "step0" 
                            for symbol in symbols:
                                sub_str_depth = ujson.dumps({"id": self.id, "sub": f"market.{symbol}.depth.{type}"})
                                # sub_str_bbo = ujson.dumps({"id": self.id, "sub": f"market.{symbol}.bbo"})
                                self.id += 1
                                await conn.send_str(sub_str_depth)                                          

                    asyncio.create_task(self.ping(conn))


                    while True:
                        
                        # 接受消息
                        try:
                            msg = await conn.receive(timeout=30) #
                        except asyncio.TimeoutError:
                            log(Color('HuobiSpot ws长时间没有收到消息 准备重连...', -1))
                            break
                        except:
                            print(traceback.format_exc())
                            log(Color('HuobiSpot ws出现错误 准备重连...', -1))
                            time.sleep(3)
                            break
                        
                        try:
                            if auth:
                                msg = ujson.loads(msg.data)
                            else:
                                msg = ujson.loads(gzip.decompress(msg.data).decode())
                        except:
                            print('解析失败: '+str(msg))
                            break
                        
                        if 'ch' in msg:  
                            if 'depth' in msg['ch']: self._update_depath(msg)
                            elif 'accounts.update#0' == msg['ch']: self._update_account(msg['data'])
                            elif 'orders.update#' == msg['ch'][:14]: self._update_order(msg['data'])
                            # elif 'trade.clearing#' == msg['ch'][:14]: self._update_order(msg['data'])
                            else: pprint(msg)
                        
                        if 'ping' in msg:
                            await conn.send_str(ujson.dumps({"pong":msg['ping']}))
    

            except Exception as e:
                # uploadError(traceback.format_exc())
                log(f'HuobiSpot ws连接失败 开始重连...', symbols, traceback.format_exc())

                time.sleep(1)
                #强制重启
                # os._exit(0)


if __name__ == "__main__":
    import huobi_spot
    # logging.getLogger("asyncio").setLevel(logging.DEBUG)
    ks = huobi_spot.huobi_spot()
    k = huobi_spot_ws(ks)
    loop = asyncio.get_event_loop()
    asyncio.run(k.main(['btcusdt'],auth=1))
