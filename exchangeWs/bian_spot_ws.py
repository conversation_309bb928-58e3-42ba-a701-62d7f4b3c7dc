#!/usr/bin/python3
# *_* coding= utf-8 *_*
import aiohttp
import asyncio
import json
import ujson
import hmac
import time
import hashlib
import traceback
from main.config import *
from main.hanshu import *


class bian_spot_ws():
    def __init__(self, bnb, depathCallback=0, spot=0, ccyBi=0):
        self.data = {}
        self.orders = {}
        self.bnb = bnb
        self.spot = spot
        self.expired_time = 300
        self.depathCallback = depathCallback
        self.ccyBi = ccyBi if ccyBi else CcyBi

        all = self.bnb.GetYuer(p=0)
        self.usdt = all['all']
        self.yingkui = all['yingkui']
        self.keyong = all['keyong']
        self.pos = {}
    

    """ 查询订单信息，备用Res"""
    # def getOrder(self, orderId, symbol=0, data=0):
    #     status = False
    #     order_liang = 0
    #     order_jia = 0

    #     if orderId in self.orders.keys():
    #         if data:
    #             return self.orders[orderId]

    #         status = self.orders[orderId]['status']
    #         order_liang = self.orders[orderId]['liang']
    #         order_jia = self.orders[orderId]['jiage']

    #     else:
    #         order = self.bnb.GetOrder(orderId, symbol)
    #         if data:
    #             return order

    #         if order:
    #             status = order['status']
    #             order_liang = order['liang']
    #             order_jia = order['jiage']
    #             self.orders[orderId] = order
    #         else:
    #             status = 'False'


    #     return status, order_liang, order_jia


    """ 更新费率"""
    def _update_feilv(self, msg):
        msg = ujson.loads(msg)['data']
        s = msg['s']
        if s not in self.data:
            self.data[s] = {}
        self.data[s]['feilv'] = float(msg['r']) * 100


    """ 更新盘口信息"""
    def _update_depth(self, msg):
        msg = msg['data']
        s = msg['s']
        x = X(s)
        if s not in self.data:
            self.data[s] = {'feilv': self.bnb.GetSymbolFeilv(s)}
        
        self.data[s]['bidPx'] = float(msg['b'])  / x
        self.data[s]['bidQty'] = float(msg['B']) * x
        self.data[s]['askPx'] = float(msg['a'])  / x
        self.data[s]['askQty'] = float(msg['A']) * x
        self.data[s]['t'] = NowTime_ms()

        if self.depathCallback:
            self.depathCallback(s, self.data[s], 'BianSwap')


    """ 更新订单"""
    def _update_order(self, msg):
        try:
            if len(self.orders) > 10000:
                for id in list(self.orders.keys()):
                    if "time" not in self.orders[id].keys() or type(self.orders[id]['time']) != int:
                        self.orders = {}
                        break
                        
                    if self.orders[id] or t - self.orders[id]['time'] > 4 * 60 * 60 * 1000:   #4h
                        del self.orders[id]

            msg = ujson.loads(msg)
            data2 = msg['o']
            if data2['X'] == 'EXPIRED' and data2['i'] in self.orders:
                data2['L'] = self.orders[data2['i']]['jiage']

            symbol = data2['s']
            x = X(symbol)
            data2['rp'] = float(data2['rp'])
            data2['n'] = float(data2['n']) if 'n' in data2 else 0
            self.orders[data2['i']] = {
                'symbol': symbol,            #交易对
                'status': data2['X'],            #状态
                'jiage': float(data2['L']) / x,      #价格
                'liang': float(data2['z']) * x,      #数量
                'side': data2['S'],              #方向
                'yingkui': float(data2['rp']),   #盈亏
                'time': msg['E'],              #推送时间
            }

            name = '开空' if data2['S'] == 'SELL' else '开多'
            if data2['R']:
                name = '平多' if data2['S'] == 'SELL' else '平空'

            jiazhi = float(data2['p'])*float(data2['q'])
            jiazhi2 = float(data2['z'])*float(data2['L'])
            if symbol[-4:] == self.ccyBi:
                msg = [GetTime(echoMs=1), '[币安订单]', symbol, name, data2['o'], data2['f'], "　ID", data2['i'], data2['X'], '　方向', data2['S'],
                '　价格', data2['p'],'　数量', data2['q']+U(jiazhi, kuo=1),
                '　成交价格',data2['L'],'　成交数量', str(data2['z'])+U(jiazhi2, kuo=1), '　盈亏', data2['rp'],'　手续费', data2['n']]

                if jiazhi2:
                    del msg[0]
                    log(*msg)

                    """ 更新可用金额，Rest太慢"""
                    if not data2['R'] and data2['X'] in ['FILLED', 'EXPIRED']:
                        self.keyong -= jiazhi2/SetGangGan*1.1

                else:
                    print(*msg)

        except Exception as e:
            uploadError(traceback.format_exc())


    """ 更新余额"""
    def _update_account(self, msg):
        for v in msg['a']['B']:
            if v['a'] == self.ccyBi:
                print("[币安余额更新]", v['wb'])
        
        for v in msg['a']['P']:
            side = 'BUY' if v['ps'] == 'LONG' else 'SELL'
            side2 = 'SELL' if v['ps'] == 'LONG' else 'BUY'
            
            x = X(v['s'])

            self.pos[f"{v['s']}_{side}"] =  {
                'symbol': v['s'],
                'side': side,
                'side2': side2,
                'posSide': v['ps'].lower(),
                'liang': abs(float(v['pa'])) * x,
                'jiage': float(v['ep']) / x,
                'nowJiage': 0,
                'qiangJiage': 0,
                'roe': 0,
                'yingkui': float(v['up']),
                'bzj': float(v['iw']),
                'update_time': msg['E'],
            }


    async def ping(self, conn):
        while 1:
            await conn.send_str('pong')
            await asyncio.sleep(10)


    async def main(self, symbols=[]):
        while True:
            # log("币安 准备连接ws", symbols)
            try:
                url = 'wss://stream.binance.com:9443'

                urls = ''
                if symbols:
                    for symbol in symbols:
                        urls += symbol.lower()+"@bookTicker/"
                    urls = urls.strip('/')

                    url += "/stream?streams="+urls

                else:
                    listen_key = self.bnb.GetlistenKey()
                    listenKeyTime = time.time()
                    url += f"/ws/{listen_key}"
                    log('[币安Ws] 连接...', url)

                async with aiohttp.ClientSession(
                        connector = aiohttp.TCPConnector(
                                limit=50,
                                keepalive_timeout=120,
                                verify_ssl=False,
                                # local_addr=(self.ip,0)
                            )
                        ).ws_connect(
                            url,
                            proxy=None,
                            timeout=30,
                            # receive_timeout=30,
                ) as conn:

                    # note: important to keepalive 

                    # 不能发错误信息 否则后面的消息全是none
                    # asyncio.create_task(self.ping(conn))

                    # loop to process update data
                    while True:
                        
                        # 接受消息
                        try:
                            msg = await conn.receive(timeout=30) #
                        except asyncio.CancelledError:
                            log('ws取消')
                            return
                        except asyncio.TimeoutError:
                            log(f'币安 ws长时间没有收到消息 准备重连...')
                            break
                        except:
                            print(traceback.format_exc())
                            log(f'币安 ws出现错误 准备重连...')
                            time.sleep(3)
                            break
                        
                        msg = msg.data

                        if not msg:
                            # log(f'币安spot ws收到空白消息 准备重连...')
                            continue

                        if msg == 'pong':
                            continue
                        
                        try:
                            msg = ujson.loads(msg)
                        except:
                            # log(f'币安spot 消息解析失败', msg)
                            continue

                        if 'stream' not in msg:
                            continue

                        if 'bookTicker' in msg['stream']:
                            self._update_depth(msg)
                        continue



                        if 'listenKeyExpired' in msg: raise Exception('币安Wskey过期重连')
                        elif 'ping' in msg:await conn.send_str('pong')
                        elif 'bookTicker' in msg: self._update_depth(msg)
                        elif 'ORDER_TRADE_UPDATE' in msg: self._update_order(msg)
                        elif 'ACCOUNT_UPDATE' in msg: self._update_account(msg)
                        elif 'markPriceUpdate' in msg: self._update_feilv(msg)

                        
                        if not symbols:
                            if time.time() - listenKeyTime > 60*15: # 每15分钟续一次
                                log('[币安Ws] 续期listenKey', self.bnb.PutlistenKey())
                                listenKeyTime = time.time()


            except Exception as e:
                uploadError(traceback.format_exc())
                log(f'币安 ws连接失败 开始重连...')


if __name__ == "__main__":
    # logging.getLogger("asyncio").setLevel(logging.DEBUG)
    loop = asyncio.get_event_loop()
    asyncio.run(main())
    # loop.set_debug(enabled=True)
    print("websocket demo")