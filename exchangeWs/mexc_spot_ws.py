#!/usr/bin/python3
# *_* coding= utf-8 *_*
import aiohttp
import asyncio
import json
import os
import ujson
import hmac
import time
import hashlib
import traceback

from main.config import *
from main.hanshu import *


from moneyV2.money import changePos

def sort_num(n):
    if n.isdigit():
        return int(n)
    else:
        return float(n)

def update_bids(res, bids_p):
    # 获取增量bids数据
    bids_u = res
    # print('增量数据bids为：' + str(bids_u))
    # print('档数为：' + str(len(bids_u)))
    # bids合并
    for i in bids_u:
        bid_price = i[0]
        for j in bids_p:
            if bid_price == j[0]:
                if i[1] == '0':
                    bids_p.remove(j)
                    break
                else:
                    del j[1]
                    j.insert(1, i[1])
                    break
        else:
            if i[1] != "0.00":
                bids_p.append(i)
    else:
        bids_p.sort(key=lambda price: sort_num(price[0]), reverse=True)
        # print('合并后的bids为：' + str(bids_p) + '，档数为：' + str(len(bids_p)))
    return bids_p


def update_asks(res, asks_p):
    # 获取增量asks数据
    asks_u = res
    # print('增量数据asks为：' + str(asks_u))
    # print('档数为：' + str(len(asks_u)))
    # asks合并
    for i in asks_u:
        ask_price = i[0]
        for j in asks_p:
            if ask_price == j[0]:
                if i[1] == '0':
                    asks_p.remove(j)
                    break
                else:
                    del j[1]
                    j.insert(1, i[1])
                    break
        else:
            if i[1] != "0.00":
                asks_p.append(i)
    else:
        asks_p.sort(key=lambda price: sort_num(price[0]))
        print('合并后的asks为：' + str(asks_p) + '，档数为：' + str(len(asks_p)))
    return asks_p


class mexc_spot_ws():
    def __init__(self, coinex, depathCallback=0, ccyBi=0):
        self.coinex = coinex
        self.access_id = coinex.access_id
        self.secret_key = coinex.secret_key
        self.depathCallback = depathCallback

        self.usdt = 0.01
        self.yingkui = 0.01
        self.keyong = 0.01
        self.preUsdt = 0.01

        self.orders = {}
        self.data = {}
        self.depth = {}
        self.pos = []

        # 过期检查
        self.public_update_time = time.time()
        self.private_update_time = time.time()


    """ 更新成交订单"""
    def _update_order_ok(self, symbol, data2):

        pprint(data2)
        
        id = data2['i']
        side = 'BUY' if data2['S'] == 1 else 'SELL'
        close = '减仓' if side == 'SELL' else '开仓'

        jiazhi = N(float(data2['p']) * float(data2['v']))

        self.orders[id] = {
            'id': id,                  #交易对
            'symbol': symbol,                  #交易对
            'status': 1,                  #状态
            'side': side,                               #方向
            'type': data2['m'],                     #订单类型
            'jiage': float(data2['p']),             #累积成交均价
            'liang': float(data2['v']),         #累积成交量
            'yingkui': 0,             #盈亏
            'jiazhi': jiazhi,  #价值
            'sxf': 0,  #手续费
            'time': NowTime(),                #推送时间
        }

        pprint(data2)
        pprint(self.orders[id])
        print("------------------")

        msg = ['[Mexc现货监听订单]', GetTime(echoMs=1), '', close, side, data2['m'],
        "　ID", id, 
        '　成交价', data2['p'], '成交量', str(data2['v']) + ' ('+U(jiazhi)+'$)']

        log(*msg)   



    """ 更新订单"""
    def _update_order(self, symbol, data2):
        try:
            id = data2['i']
            side = 'BUY' if data2['S'] == 1 else 'SELL'
            close = '减仓' if side == 'SELL' else '开仓'

            jiage = float(data2['ap']) if 'ap' in data2 else 0
            liang = float(data2['cv']) if 'cv' in data2 else 0
            jiazhi = float(data2['ca']) if 'ca' in data2 else 0
            status = {1:'未成交', 2:'已成交', 3:'部分成交', 4:'已撤单', 5:'部分撤单'}
            orType = {1:'LIMIT_ORDER', 2:'POST_ONLY', 3:'IMMEDIATE_OR_CANCEL', 4:'FILL_OR_KILL', 5:'MARKET_ORDER', 100:'止盈止损'}


            self.orders[id] = {
                'id': id,                  #交易对
                'symbol': symbol,                  #交易对
                'status': data2['s'],                  #状态
                'side': side,                               #方向
                'type': data2['o'],                     #订单类型
                'jiage': jiage,             #累积成交均价
                'liang': liang,         #累积成交量
                'yingkui': 0,             #盈亏
                'jiazhi': jiazhi,  #价值
                'sxf': 0,  #手续费
                'time': NowTime(),                #推送时间
            }

            # pprint(data2)
            # pprint(self.orders[id])
            # print("------------------")

            msg = ['[Mexc现货监听订单]', GetTime(echoMs=1), '', close, side, status[data2['s']],
            "　ID", id, orType[data2['o']],
            '　下单价', data2['p'], '下单量', str(data2['v']) + ' ('+U(data2['a'])+')',
            '　成交价', jiage, '成交量', str(liang) + ' ('+U(jiazhi)+')']


            if jiazhi:
                log(*msg)
                if data2['s'] in self.order_status:
                    changePos(self, symbol, side, jiage, liang)

            else:
                print(*msg)


            
            if len(self.orders) > 500:
                self.orders = dict(list(self.orders.items())[-100:])

            self.private_update_time = time.time()
        except Exception as e:
            uploadError(traceback.format_exc())



    """ 更新余额"""
    def _update_account(self, data):
        pass
        


    """ 更新盘口和费率信息"""
    def _update_depth(self, s, v):
        if not v['b']:
            return
            
        self.data[s] = {
            'bidPx': float(v['b']),
            'askPx': float(v['a']),
            'bidQty': float(v['B']),
            'askQty': float(v['A']),
            'maxLiang': MAX,
            'feilv': 0,
            't': NowTime_ms()
        }

        if self.depathCallback:
            self.depathCallback(s, self.data[s])
            

    async def ping(self, conn, sub_str):
        while True:
            await conn.send_str('{"method":"PING"}')
            await asyncio.sleep(10)


    """ 定时延长Key"""
    async def putKey(self, key):
        while True:
            await asyncio.sleep(20 * 60)    #20m

            self.coinex.PutKey(key)



    async def main(self, symbols=[]):
        while True:
            try:
                
                if not symbols:
                    key = self.coinex.CreateKey()
                    url = 'wss://wbs.mexc.com/ws' + '?listenKey=' + key
                
                else:
                    key = ''
                    url = 'wss://wbs.mexc.com/ws'

                async with aiohttp.ClientSession(
                        connector = aiohttp.TCPConnector(
                                limit=50,
                                keepalive_timeout=120,
                                verify_ssl=False,
                                # local_addr=(self.ip,0)
                            )
                        ).ws_connect(
                            url,
                            proxy=None,
                            timeout=30,
                            receive_timeout=30,
                ) as conn:
                    # print(url, symbols, '成功')

                    if symbols:
                        channels = []
                        for symbol in symbols:
                            channels += [
                                f"<EMAIL>@{symbol}"
                            ]

                    else:
                        # 订阅
                        channels = [
                            # "<EMAIL>",
                            "<EMAIL>",
                        ]

                    params = {
                        "method": "SUBSCRIPTION",
                        "params": channels
                    }

                    sub_str = ujson.dumps(params)
                    await conn.send_str(sub_str)

                    msg = await conn.receive(timeout=30)
                    if not symbols:
                        log('Mexc链接WS', msg.data)

                    asyncio.create_task(self.ping(conn, sub_str))
                    if key:
                        asyncio.create_task(self.putKey(key))


                    while True:
                        
                        # 接受消息
                        try:
                            msg = await conn.receive(timeout=30) #
                        except asyncio.TimeoutError:
                            log(Color('MexcSpot ws长时间没有收到消息 准备重连...', -1))
                            break
                        except:
                            print(traceback.format_exc())
                            log(Color('MexcSpot ws出现错误 准备重连...', -1))
                            time.sleep(3)
                            break
                        
                        msg = msg.data
                        # print('Ws-->', msg)

                        try:
                            msg = ujson.loads(msg)
                            # pprint(msg)

                        except:
                            print('MexcSpot 解析失败: '+str(msg))
                            break

                        if 'c' in msg:

                            # if 'private.bookTicker' in msg['c']: self._update_order_ok(msg['s'], msg['d'])
                            if 'private.orders' in msg['c']: self._update_order(msg['s'], msg['d'])
                            elif 'public.bookTicker' in msg['c']:self._update_depth(msg['s'], msg['d'])


            except Exception as e:
                uploadError(traceback.format_exc())
                log(f'Ku ws连接失败 开始重连...')

                #强制重启
                os._exit(0)


if __name__ == "__main__":
    # logging.getLogger("asyncio").setLevel(logging.DEBUG)
    loop = asyncio.get_event_loop()
    asyncio.run(main())
    # loop.set_debug(enabled=True)
    print("websocket demo")