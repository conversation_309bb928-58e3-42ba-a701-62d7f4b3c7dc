#!/usr/bin/python3
# *_* coding= utf-8 *_*
import aiohttp
import asyncio
import json
import os
import ujson
import hmac
import time
import hashlib
import traceback

from main.config import *
from main.hanshu import *


from moneyV2.money import changePos

def sign(message, secret_key):
    mac = hmac.new(bytes(secret_key, encoding='utf8'), bytes(message, encoding='utf-8'), digestmod='sha256')
    d = mac.digest()
    return str(base64.b64encode(d), 'utf8')

def pre_hash(timestamp, method, request_path):
    return str(timestamp) + str.upper(method) + str(request_path)


class bybit_ws():
    def __init__(self, gate, depathCallback=0, ccyBi=0):
        self.depathCallback = depathCallback
        self.gate = gate
        self.access_id = gate.access_id
        self.secret_key = gate.secret_key
        
        self.BaseURL_public = "wss://stream.bybit.com/contract/usdt/public/v3"
        self.BaseURL_private = "wss://stream.bybit.com/contract/private/v3"

        all = self.gate.GetYuer(p=0)
        self.usdt = all['all']
        self.yingkui = all['yingkui']
        self.keyong = all['keyong']
        self.preUsdt = 0.01

        self.orders = {}
        self.symbol = {}
        self.data = {}
        self.pos = []

        # 过期检查
        self.public_update_time = time.time()
        self.private_update_time = time.time()


    """ 更新订单"""
    def _update_order(self, data):
        try:
            """Created
            New
            Rejected
            PartiallyFilled
            Filled
            PendingCancel - 撮合引擎收到取消指令但不一定會被成功取消
            Cancelled"""
            for data2 in data:
                id = data2['orderId']
                symbol = data2['symbol']
                side = data2['side'].upper()
                close = '减仓' if data2['reduceOnly'] else '开仓'

                jiage = 0
                liang = float(data2['cumExecQty'])
                jiazhi = float(data2['cumExecValue'])
                if liang:   #累计成交价值
                    jiage = jiazhi / liang
                    
                self.orders[id] = {
                    'id': id,                  #交易对
                    'symbol': symbol,                  #交易对
                    'status': data2['orderStatus'],                  #状态
                    'side': side,                               #方向
                    'type': data2['orderType'],                     #订单类型
                    'jiage': jiage,             #累积成交均价
                    'liang': liang,         #累积成交量
                    'yingkui': 0,             #盈亏
                    'jiazhi': jiazhi,  #价值
                    'sxf': 0,  #手续费
                    'time': NowTime(),                #推送时间
                }

                # pprint(data)
                # pprint(self.orders[id])
                # print("------------------")

                #价格小数位
                if symbol in self.symbol:
                    J = self.symbol[symbol]['J']
                else:
                    J = WeiShu(data2['price'])

                msg = ['[Bybit监听订单]', GetTime(echoMs=1), '', close, side, symbol, data2['orderType'], data2['timeInForce'],
                "　ID", id, data2['orderStatus'],
                '　价格', data2['price'], '数量', data2['qty'] + ' ('+N_STR(float(data2['price'])*float(data2['qty']), 2)+'$)',
                '　成交价', N(jiage, J), '成交量', str(liang) + ' ('+N_STR(jiazhi, 2)+'$)']

                if liang:
                    log(*msg)
                    
                    if data2['orderStatus'] in ['Filled', 'PendingCancel', 'Cancelled', 'Rejected']:
                        changePos(self, symbol, side, jiage, liang)
                else:
                    print(*msg)

            if len(self.orders) > 500:
                self.orders = dict(list(self.orders.items())[-100:])

            self.private_update_time = time.time()
        except Exception as e:
            uploadError(traceback.format_exc())




    """ 更新持仓"""
    def _update_pos(self, data):
        pprint(data)
        
        okdata = []
        for v in data:

            ok = {}
            ok['symbol'] = v['symbol']
            ok['liang'] = float(v['size'])
            if not ok['liang']:
                continue

            ok['side'] = v['side'].upper()
            ok['side2'] = 'SELL' if ok['side'] == 'BUY' else 'BUY'

            ok['jiage'] = float(v['entryPrice'])
            ok['nowJiage'] = 0    #最新成交价格
            ok['yingkui'] = N(float(v['unrealisedPnl']), 4)
            ok['bzj'] = 0
            ok['create_time'] = int(v['createdTime']) / 1000
            ok['roe'] = 0
            ok['time'] = NowTime_ms()

            okdata.append(ok)

        self.pos = okdata
        # pprint(self.pos)


    """ 更新余额"""
    def _update_account(self, data):

        for i in data:
            if i['coin'].upper() == CcyBi:
                self.usdt = N(float(i['equity']), 4)
                self.keyong = N(float(i['availableBalance']), 4)
                self.yingkui = N(i['unrealisedPnl'], 4)

                self.usdt = self.usdt if self.usdt else 0.01
                self.keyong = self.keyong if self.keyong else 0.01

                if self.usdt != self.preUsdt:
                    print('[Bybit余额更新]', self.usdt, '可用', self.keyong, '盈亏', self.yingkui)

                self.preUsdt = self.usdt
        


    """ 更新盘口和费率信息"""
    def _update_depth(self, v):
        s = v['s']
        if s not in self.data:
            self.data[s] = {
                'bidPx': 0,
                'bidQty': 0,
                'askPx': 0,
                'askQty': 0,
                'feilv': 0,
                't': NowTime_ms()
            }
        
        if v['b']:  #买方
            self.data[s]['bidPx'] = float(v['b'][0][0])
            self.data[s]['bidQty'] = float(v['b'][0][1])

        if v['a']:  #卖方
            self.data[s]['askPx'] = float(v['a'][0][0])
            self.data[s]['askQty'] = float(v['a'][0][1])
        
        self.data[s]['t'] = NowTime_ms()

        if self.data[s]['bidPx'] and self.data[s]['askPx'] and self.depathCallback:
            self.depathCallback(s, self.data[s])



    async def ping(self, conn):
        while True:
            await conn.send_str('{"op": "ping"}')
            await asyncio.sleep(5)


    async def main(self, symbols=[]):
        while True:
            try:

                async with aiohttp.ClientSession(
                        connector = aiohttp.TCPConnector(
                                limit=50,
                                keepalive_timeout=120,
                                verify_ssl=False,
                                # local_addr=(self.ip,0)
                            )
                        ).ws_connect(
                            self.BaseURL_private if not symbols else self.BaseURL_public,
                            proxy=None,
                            timeout=30,
                            receive_timeout=30,
                ) as conn:

                    channels = []

                    if symbols:
                        for symbol in symbols:
                            channels += [
                                f"orderbook.1.{symbol}"
                            ]

                    else:
                        # 先鉴权
                        expires = int((time.time() + 10000) * 1000)
                        signature = str(hmac.new(
                            bytes(self.secret_key, "utf-8"),
                            bytes(f"GET/realtime{expires}", "utf-8"), digestmod="sha256"
                        ).hexdigest())
                        await conn.send_str(ujson.dumps({
                            "op":"auth",
                                "args":[
                                    self.access_id, expires, signature
                                ]
                        }))
                        res = await conn.receive(timeout=30)
                        log('Bybit ws login', json.loads(res.data))
                            
                        # 订阅
                        channels = [
                            # "user.position.contractAccount",  #他是增量更新的
                            "user.wallet.contractAccount",
                            "user.order.contractAccount",
                        ]


                    for i in channels:
                        sub_str = ujson.dumps({"args": [i], "op":"subscribe"})
                        await conn.send_str(sub_str)

                    asyncio.create_task(self.ping(conn))


                    while True:
                        
                        # 接受消息
                        try:
                            msg = await conn.receive(timeout=30) #
                        except asyncio.TimeoutError:
                            log(Color('Bybit ws长时间没有收到消息 准备重连...', -1))
                            break
                        except:
                            print(traceback.format_exc())
                            log(Color('Bybit ws出现错误 准备重连...', -1))
                            time.sleep(3)
                            break
                        
                        msg = msg.data
                        if 'pong' in msg:
                            continue

                        try:
                            msg = ujson.loads(msg)
                        except:
                            print('解析失败: '+str(msg))
                            break
                        if 'topic' in msg:
                            
                            # if 'orderbook' not in msg['topic']:
                            #     print(msg)
                            if 'orderbook' in msg['topic']:self._update_depth(msg['data'])
                            elif 'user.wallet.contractAccount' in msg['topic']:self._update_account(msg['data'])
                            # elif 'user.position.contractAccount' in msg['topic']:self._update_pos(msg['data'])
                            elif 'user.order.contractAccount' in msg['topic']:self._update_order(msg['data'])
                            else: pprint(msg)
                        # else:
                        #     print(msg)


            except Exception as e:
                uploadError(traceback.format_exc())
                log(f'Bybit ws连接失败 开始重连...')
                time.sleep(15)

                #强制重启
                os._exit(0)



if __name__ == "__main__":
    # logging.getLogger("asyncio").setLevel(logging.DEBUG)
    loop = asyncio.get_event_loop()
    asyncio.run(main())
    # loop.set_debug(enabled=True)
    print("websocket demo")